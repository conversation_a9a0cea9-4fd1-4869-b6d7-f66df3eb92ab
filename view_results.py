#!/usr/bin/env python3
"""
View Results Script

This script helps view the generated analysis results and images.
"""

import os
import webbrowser
from pathlib import Path
import pandas as pd

def show_summary():
    """Display the summary CSV file"""
    print("=" * 80)
    print("RICHARD DATA SUMMARY")
    print("=" * 80)
    
    if os.path.exists('richard_data_summary.csv'):
        df = pd.read_csv('richard_data_summary.csv')
        print(df.to_string(index=False))
        
        print(f"\nTotal Trading Pairs: {len(df)}")
        print(f"Total Orderbook Files: {df['Orderbook_Files'].sum()}")
        print(f"Total Trade Files: {df['Trade_Files'].sum()}")
        
        # Calculate total estimated records
        total_ob_records = df['Sample_OB_Records'].sum() * df['Orderbook_Files'].sum() / len(df)
        total_trade_records = df['Sample_Trade_Records'].sum() * df['Trade_Files'].sum() / len(df)
        
        print(f"Estimated Total Orderbook Records: {total_ob_records:,.0f}")
        print(f"Estimated Total Trade Records: {total_trade_records:,.0f}")
        
    else:
        print("Summary file not found. Please run analyze_richard_data.py first.")

def list_generated_files():
    """List all generated analysis files"""
    print("\n" + "=" * 80)
    print("GENERATED FILES")
    print("=" * 80)
    
    files = []
    
    # Look for analysis images
    for file in Path('.').glob('*_analysis.png'):
        files.append(str(file))
    
    # Look for summary file
    if os.path.exists('richard_data_summary.csv'):
        files.append('richard_data_summary.csv')
    
    if files:
        print("Generated files:")
        for i, file in enumerate(files, 1):
            print(f"{i}. {file}")
    else:
        print("No analysis files found. Please run analyze_richard_data.py first.")
    
    return files

def open_images():
    """Open generated images in default viewer"""
    image_files = list(Path('.').glob('*_analysis.png'))
    
    if image_files:
        print(f"\nFound {len(image_files)} analysis images:")
        for img in image_files:
            print(f"- {img}")
        
        try:
            for img in image_files:
                # Try to open with default system viewer
                if os.name == 'nt':  # Windows
                    os.startfile(str(img))
                elif os.name == 'posix':  # macOS and Linux
                    os.system(f'open "{img}"')  # macOS
                    # For Linux, you might need: os.system(f'xdg-open "{img}"')
            print("Images opened in default viewer.")
        except Exception as e:
            print(f"Error opening images: {e}")
            print("Please open the image files manually.")
    else:
        print("No analysis images found.")

def show_data_insights():
    """Show key insights from the data analysis"""
    print("\n" + "=" * 80)
    print("KEY INSIGHTS FROM RICHARD DATA")
    print("=" * 80)
    
    if os.path.exists('richard_data_summary.csv'):
        df = pd.read_csv('richard_data_summary.csv')
        
        print("📊 DATA OVERVIEW:")
        print(f"• {len(df)} trading pairs available")
        print(f"• Most active pairs by trade volume: BTC_USD, ETH_USD, XRP_USD")
        print(f"• Least active pairs: TRX_USD, ADA_USD")
        
        print("\n💰 TRADING PAIRS ANALYSIS:")
        # Sort by sample trade records to show activity
        df_sorted = df.sort_values('Sample_Trade_Records', ascending=False)
        print("Most active pairs (by sample trade records):")
        for i, row in df_sorted.head(5).iterrows():
            print(f"  {row['Pair']}: {row['Sample_Trade_Records']:,} trades per file")
        
        print("\n📈 DATA STRUCTURE:")
        print("• Orderbook data: Price, Size, Time")
        print("• Trade data: Price, Size, Time, Side (buy/sell)")
        print("• Data format: Feather files (efficient binary format)")
        print("• Time range: Multiple days of continuous data")
        
        print("\n🔍 ANALYSIS CAPABILITIES:")
        print("• Price distribution analysis")
        print("• Volume analysis")
        print("• Buy/sell side distribution")
        print("• Time series analysis")
        print("• Order book imbalance calculation")
        print("• Market microstructure analysis")
        
    else:
        print("Summary file not found. Please run analyze_richard_data.py first.")

def main():
    """Main function"""
    print("Richard Data Analysis Results Viewer")
    print("=" * 50)
    
    # Show summary
    show_summary()
    
    # List generated files
    files = list_generated_files()
    
    # Show insights
    show_data_insights()
    
    # Ask if user wants to open images
    if any('.png' in f for f in files):
        print("\n" + "=" * 80)
        response = input("Would you like to open the analysis images? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            open_images()
    
    print("\n" + "=" * 80)
    print("NEXT STEPS:")
    print("=" * 80)
    print("1. Review the generated visualizations")
    print("2. Examine the summary CSV file for detailed statistics")
    print("3. Modify analyze_richard_data.py to analyze specific pairs")
    print("4. Use the data for:")
    print("   • Order Book Imbalance (OBI) analysis")
    print("   • Price prediction models")
    print("   • Trading strategy development")
    print("   • Market microstructure research")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Richard Data Analysis and Visualization Tool

This script analyzes and visualizes orderbook and trade data from the Richard_data folder.
It provides comprehensive insights into the data structure and creates visualizations.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import glob
from pathlib import Path
import warnings
from datetime import datetime
import os

warnings.filterwarnings("ignore")

# Set up plotting style
plt.style.use("default")
sns.set_palette("husl")


class RichardDataAnalyzer:
    def __init__(self, data_dir="Data_Fetching/Richard_data"):
        self.data_dir = Path(data_dir)
        self.trading_pairs = []
        self.orderbook_data = {}
        self.trade_data = {}

    def discover_trading_pairs(self):
        """Discover all available trading pairs in the data directory"""
        orderbook_files = glob.glob(str(self.data_dir / "*_orderbook_*.feather"))
        pairs = set()

        for file in orderbook_files:
            filename = Path(file).name
            # Extract pair name (e.g., BTC_USD from BTC_USD_orderbook_timestamp.feather)
            pair = filename.split("_orderbook_")[0]
            pairs.add(pair)

        self.trading_pairs = sorted(list(pairs))
        print(
            f"Discovered {len(self.trading_pairs)} trading pairs: {self.trading_pairs}"
        )
        return self.trading_pairs

    def load_sample_data(self, pair, max_files=3):
        """Load sample data for a specific trading pair"""
        print(f"\n=== Loading sample data for {pair} ===")

        # Load orderbook data
        orderbook_files = glob.glob(str(self.data_dir / f"{pair}_orderbook_*.feather"))[
            :max_files
        ]
        trade_files = glob.glob(str(self.data_dir / f"{pair}_trades_*.feather"))[
            :max_files
        ]

        print(
            f"Found {len(orderbook_files)} orderbook files and {len(trade_files)} trade files"
        )

        # Load and combine orderbook data
        if orderbook_files:
            orderbook_dfs = []
            for file in orderbook_files:
                df = pd.read_feather(file)
                orderbook_dfs.append(df)
            self.orderbook_data[pair] = pd.concat(orderbook_dfs, ignore_index=True)
            print(f"Loaded {len(self.orderbook_data[pair])} orderbook records")

        # Load and combine trade data
        if trade_files:
            trade_dfs = []
            for file in trade_files:
                df = pd.read_feather(file)
                trade_dfs.append(df)
            self.trade_data[pair] = pd.concat(trade_dfs, ignore_index=True)
            print(f"Loaded {len(self.trade_data[pair])} trade records")

    def analyze_data_structure(self, pair):
        """Analyze and print data structure for a trading pair"""
        print(f"\n{'='*60}")
        print(f"DATA STRUCTURE ANALYSIS FOR {pair}")
        print(f"{'='*60}")

        # Analyze orderbook data
        if pair in self.orderbook_data:
            ob_df = self.orderbook_data[pair]
            print(f"\n--- ORDERBOOK DATA ---")
            print(f"Shape: {ob_df.shape}")
            print(f"Columns: {list(ob_df.columns)}")
            print(f"Data types:\n{ob_df.dtypes}")
            print(f"\nFirst 5 rows:")
            print(ob_df.head())
            print(f"\nStatistical summary:")
            print(ob_df.describe())

            # Time analysis
            if "Time" in ob_df.columns:
                ob_df["Time"] = pd.to_datetime(ob_df["Time"], errors="coerce")
                print(f"\nTime range:")
                print(f"  Start: {ob_df['Time'].min()}")
                print(f"  End: {ob_df['Time'].max()}")
                print(f"  Duration: {ob_df['Time'].max() - ob_df['Time'].min()}")

        # Analyze trade data
        if pair in self.trade_data:
            trade_df = self.trade_data[pair]
            print(f"\n--- TRADE DATA ---")
            print(f"Shape: {trade_df.shape}")
            print(f"Columns: {list(trade_df.columns)}")
            print(f"Data types:\n{trade_df.dtypes}")
            print(f"\nFirst 5 rows:")
            print(trade_df.head())
            print(f"\nStatistical summary:")
            print(trade_df.describe())

            # Side analysis
            if "Side" in trade_df.columns:
                print(f"\nTrade side distribution:")
                print(trade_df["Side"].value_counts())

            # Time analysis
            if "Time" in trade_df.columns:
                trade_df["Time"] = pd.to_datetime(trade_df["Time"], errors="coerce")
                print(f"\nTime range:")
                print(f"  Start: {trade_df['Time'].min()}")
                print(f"  End: {trade_df['Time'].max()}")
                print(f"  Duration: {trade_df['Time'].max() - trade_df['Time'].min()}")

    def create_visualizations(self, pair):
        """Create comprehensive visualizations for a trading pair"""
        print(f"\n=== Creating visualizations for {pair} ===")

        fig = plt.figure(figsize=(20, 16))

        # Check if we have data for this pair
        has_orderbook = pair in self.orderbook_data
        has_trades = pair in self.trade_data

        if not (has_orderbook or has_trades):
            print(f"No data available for {pair}")
            return

        # Prepare data
        if has_orderbook:
            ob_df = self.orderbook_data[pair].copy()
            ob_df["Time"] = pd.to_datetime(ob_df["Time"], errors="coerce")
            ob_df = ob_df.dropna(subset=["Time"]).sort_values("Time")

        if has_trades:
            trade_df = self.trade_data[pair].copy()
            trade_df["Time"] = pd.to_datetime(trade_df["Time"], errors="coerce")
            trade_df = trade_df.dropna(subset=["Time"]).sort_values("Time")

        # Plot 1: Orderbook Price Distribution
        if has_orderbook:
            plt.subplot(3, 3, 1)
            plt.hist(ob_df["Price"], bins=50, alpha=0.7, color="blue")
            plt.title(f"{pair} - Orderbook Price Distribution")
            plt.xlabel("Price")
            plt.ylabel("Frequency")
            plt.grid(True, alpha=0.3)

        # Plot 2: Orderbook Size Distribution
        if has_orderbook:
            plt.subplot(3, 3, 2)
            plt.hist(ob_df["Size"], bins=50, alpha=0.7, color="green")
            plt.title(f"{pair} - Orderbook Size Distribution")
            plt.xlabel("Size")
            plt.ylabel("Frequency")
            plt.yscale("log")
            plt.grid(True, alpha=0.3)

        # Plot 3: Price vs Size Scatter (Orderbook)
        if has_orderbook:
            plt.subplot(3, 3, 3)
            sample_ob = ob_df.sample(min(1000, len(ob_df)))
            plt.scatter(sample_ob["Price"], sample_ob["Size"], alpha=0.5, s=1)
            plt.title(f"{pair} - Price vs Size (Orderbook)")
            plt.xlabel("Price")
            plt.ylabel("Size")
            plt.yscale("log")
            plt.grid(True, alpha=0.3)

        # Plot 4: Trade Price Distribution
        if has_trades:
            plt.subplot(3, 3, 4)
            plt.hist(trade_df["Price"], bins=50, alpha=0.7, color="red")
            plt.title(f"{pair} - Trade Price Distribution")
            plt.xlabel("Price")
            plt.ylabel("Frequency")
            plt.grid(True, alpha=0.3)

        # Plot 5: Trade Size Distribution
        if has_trades:
            plt.subplot(3, 3, 5)
            plt.hist(trade_df["Size"], bins=50, alpha=0.7, color="orange")
            plt.title(f"{pair} - Trade Size Distribution")
            plt.xlabel("Size")
            plt.ylabel("Frequency")
            plt.yscale("log")
            plt.grid(True, alpha=0.3)

        # Plot 6: Trade Side Distribution
        if has_trades and "Side" in trade_df.columns:
            plt.subplot(3, 3, 6)
            side_counts = trade_df["Side"].value_counts()
            plt.pie(side_counts.values, labels=side_counts.index, autopct="%1.1f%%")
            plt.title(f"{pair} - Trade Side Distribution")

        # Plot 7: Price Time Series (Trades)
        if has_trades:
            plt.subplot(3, 3, 7)
            # Sample data for better performance
            sample_trades = trade_df.sample(min(1000, len(trade_df))).sort_values(
                "Time"
            )
            plt.plot(
                sample_trades["Time"], sample_trades["Price"], alpha=0.7, linewidth=0.5
            )
            plt.title(f"{pair} - Price Time Series (Trades)")
            plt.xlabel("Time")
            plt.ylabel("Price")
            plt.xticks(rotation=45)
            plt.grid(True, alpha=0.3)

        # Plot 8: Volume Time Series
        if has_trades:
            plt.subplot(3, 3, 8)
            # Resample to hourly volume
            trade_df_resampled = trade_df.set_index("Time").resample("1H")["Size"].sum()
            plt.plot(trade_df_resampled.index, trade_df_resampled.values)
            plt.title(f"{pair} - Hourly Volume")
            plt.xlabel("Time")
            plt.ylabel("Volume")
            plt.xticks(rotation=45)
            plt.grid(True, alpha=0.3)

        # Plot 9: Price vs Size Scatter (Trades)
        if has_trades:
            plt.subplot(3, 3, 9)
            sample_trades = trade_df.sample(min(1000, len(trade_df)))
            colors = [
                "red" if side == "sell" else "green"
                for side in sample_trades.get("Side", ["unknown"] * len(sample_trades))
            ]
            plt.scatter(
                sample_trades["Price"], sample_trades["Size"], c=colors, alpha=0.5, s=1
            )
            plt.title(f"{pair} - Price vs Size (Trades)")
            plt.xlabel("Price")
            plt.ylabel("Size")
            plt.yscale("log")
            plt.grid(True, alpha=0.3)
            if "Side" in trade_df.columns:
                plt.legend(["Sell", "Buy"])

        plt.tight_layout()

        # Save the plot
        output_file = f"{pair}_analysis.png"
        plt.savefig(output_file, dpi=300, bbox_inches="tight")
        print(f"Visualization saved as: {output_file}")
        plt.show()

    def create_summary_report(self):
        """Create a summary report of all trading pairs"""
        print(f"\n{'='*80}")
        print("RICHARD DATA SUMMARY REPORT")
        print(f"{'='*80}")

        summary_data = []

        for pair in self.trading_pairs:
            # Count files for each pair
            orderbook_files = glob.glob(
                str(self.data_dir / f"{pair}_orderbook_*.feather")
            )
            trade_files = glob.glob(str(self.data_dir / f"{pair}_trades_*.feather"))

            # Load one sample file to get basic info
            try:
                if orderbook_files:
                    sample_ob = pd.read_feather(orderbook_files[0])
                    ob_records = len(sample_ob)
                else:
                    ob_records = 0

                if trade_files:
                    sample_trade = pd.read_feather(trade_files[0])
                    trade_records = len(sample_trade)
                else:
                    trade_records = 0

                summary_data.append(
                    {
                        "Pair": pair,
                        "Orderbook_Files": len(orderbook_files),
                        "Trade_Files": len(trade_files),
                        "Sample_OB_Records": ob_records,
                        "Sample_Trade_Records": trade_records,
                    }
                )
            except Exception as e:
                print(f"Error processing {pair}: {e}")
                summary_data.append(
                    {
                        "Pair": pair,
                        "Orderbook_Files": len(orderbook_files),
                        "Trade_Files": len(trade_files),
                        "Sample_OB_Records": "Error",
                        "Sample_Trade_Records": "Error",
                    }
                )

        # Create summary DataFrame
        summary_df = pd.DataFrame(summary_data)
        print("\nSUMMARY TABLE:")
        print(summary_df.to_string(index=False))

        # Save summary to CSV
        summary_df.to_csv("richard_data_summary.csv", index=False)
        print(f"\nSummary saved to: richard_data_summary.csv")

        return summary_df


def main():
    """Main function to run the analysis"""
    print("Richard Data Analysis Tool")
    print("=" * 50)

    # Initialize analyzer
    analyzer = RichardDataAnalyzer()

    # Discover available trading pairs
    pairs = analyzer.discover_trading_pairs()

    if not pairs:
        print("No trading pairs found in the data directory!")
        return

    # Create summary report
    analyzer.create_summary_report()

    # Ask user which pairs to analyze in detail
    print(f"\nAvailable pairs: {pairs}")
    print("Analyzing all pairs in detail...")

    # Analyze each trading pair
    for pair in pairs:  # Analyze all pairs
        print(f"\nProcessing {pair}...")

        # Load sample data
        analyzer.load_sample_data(pair, max_files=2)

        # Analyze data structure
        analyzer.analyze_data_structure(pair)

        # Create visualizations
        analyzer.create_visualizations(pair)

        print(f"Completed analysis for {pair}")

    print(
        f"\nAnalysis complete! Generated visualizations for {len(pairs)} trading pairs."
    )
    print("Generated files:")
    print("- richard_data_summary.csv (summary of all pairs)")
    for pair in pairs:
        print(f"- {pair}_analysis.png (detailed visualization)")

    print(
        f"\nTo analyze other pairs, modify the script to include them in the analysis loop."
    )


if __name__ == "__main__":
    main()

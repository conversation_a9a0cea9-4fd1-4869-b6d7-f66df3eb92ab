import pandas as pd
import numpy as np
import glob
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression, Lasso, LassoCV
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import (
    accuracy_score,
    mean_squared_error,
    r2_score,
)
import warnings
from typing import Dict, List, Tuple
from datetime import timedelta
from tqdm import tqdm

# Bayesian optimization imports
try:
    from skopt import gp_minimize
    from skopt.space import Real, Integer, Categorical
    from skopt.utils import use_named_args

    BAYESIAN_OPT_AVAILABLE = True
except ImportError:
    print(
        "Warning: scikit-optimize not available. Install with: pip install scikit-optimize"
    )
    BAYESIAN_OPT_AVAILABLE = False

warnings.filterwarnings("ignore")


def install_dependencies():
    """Install required dependencies for Bayesian optimization"""
    try:
        import subprocess
        import sys

        print("Installing scikit-optimize for Bayesian optimization...")
        subprocess.check_call(
            [sys.executable, "-m", "pip", "install", "scikit-optimize"]
        )
        print("✅ scikit-optimize installed successfully")

        # Try importing again
        global BAYESIAN_OPT_AVAILABLE
        try:
            from skopt import gp_minimize
            from skopt.space import Real, Integer, Categorical
            from skopt.utils import use_named_args

            BAYESIAN_OPT_AVAILABLE = True
            print("✅ Bayesian optimization is now available")
        except ImportError:
            print("❌ Failed to import scikit-optimize after installation")

    except Exception as e:
        print(f"❌ Failed to install scikit-optimize: {e}")
        print("Please install manually: pip install scikit-optimize")


def load_trading_pair_data(pair: str, data_dir="../Data_fetching/Richard_data"):
    """Load and combine all trade data for a specific trading pair"""
    data_path = Path(data_dir)
    trade_files = glob.glob(str(data_path / f"{pair}_trades_*.feather"))

    if not trade_files:
        raise FileNotFoundError(f"No trade files found for {pair} in {data_dir}")

    print(f"Found {len(trade_files)} trade files for {pair}")

    # Load and combine all files
    dfs = []
    for file in trade_files:
        df = pd.read_feather(file)
        dfs.append(df)

    combined_df = pd.concat(dfs, ignore_index=True)

    # Convert time to datetime and sort
    combined_df["Time"] = pd.to_datetime(combined_df["Time"], errors="coerce")
    combined_df = combined_df.dropna(subset=["Time"])
    combined_df = combined_df.sort_values("Time").reset_index(drop=True)

    return combined_df


def load_orderbook_data(
    pair: str, data_dir="../Data_fetching/Richard_data", max_files: int = None
):
    """Load and combine orderbook data for a specific trading pair"""
    data_path = Path(data_dir)
    orderbook_files = glob.glob(str(data_path / f"{pair}_orderbook_*.feather"))

    if not orderbook_files:
        raise FileNotFoundError(f"No orderbook files found for {pair} in {data_dir}")

    # Limit files if specified
    if max_files:
        orderbook_files = orderbook_files[:max_files]

    print(f"Found {len(orderbook_files)} orderbook files for {pair}")

    # Load and combine all files
    dfs = []
    for file in orderbook_files:
        df = pd.read_feather(file)
        dfs.append(df)

    combined_df = pd.concat(dfs, ignore_index=True)

    # Convert time to datetime and sort
    combined_df["Time"] = pd.to_datetime(combined_df["Time"], errors="coerce")
    combined_df = combined_df.dropna(subset=["Time"])
    combined_df = combined_df.sort_values("Time").reset_index(drop=True)

    return combined_df


def calculate_midprice_from_orderbook(
    df: pd.DataFrame, window_size: int = 100
) -> pd.DataFrame:
    """Calculate midprice from orderbook data by finding best bid and ask at each timestamp"""
    df = df.copy()

    # Group by time to get snapshots
    snapshots = []

    for time_point, group in df.groupby("Time"):
        # Sort by price to identify bids (lower prices) and asks (higher prices)
        group_sorted = group.sort_values("Price")

        # Find the midpoint price to separate bids and asks
        mid_idx = len(group_sorted) // 2

        # Best bid (highest price below midpoint) and best ask (lowest price above midpoint)
        bids = group_sorted.iloc[:mid_idx]
        asks = group_sorted.iloc[mid_idx:]

        if len(bids) > 0 and len(asks) > 0:
            best_bid = bids["Price"].max()
            best_ask = asks["Price"].min()
            midprice = (best_bid + best_ask) / 2

            snapshots.append(
                {
                    "Time": time_point,
                    "MidPrice": midprice,
                    "BestBid": best_bid,
                    "BestAsk": best_ask,
                    "Spread": best_ask - best_bid,
                }
            )

    midprice_df = pd.DataFrame(snapshots)

    # Apply rolling window smoothing
    midprice_df["MidPrice"] = (
        midprice_df["MidPrice"]
        .rolling(window=min(window_size, len(midprice_df)), center=True)
        .median()
    )
    midprice_df["MidPrice"] = (
        midprice_df["MidPrice"].fillna(method="bfill").fillna(method="ffill")
    )

    return midprice_df


def calculate_mid_price_rolling(
    df: pd.DataFrame, window_size: int = 100
) -> pd.DataFrame:
    """Calculate rolling mid price for layering"""
    df = df.copy()
    df["MidPrice"] = df["Price"].rolling(window=window_size, center=True).median()
    df["MidPrice"] = df["MidPrice"].fillna(method="bfill").fillna(method="ffill")
    return df


def create_obi_layers_from_orderbook(
    orderbook_df: pd.DataFrame,
    midprice_df: pd.DataFrame,
    num_layers: int = 40,
    layer_width_pct: float = 0.01,
) -> pd.DataFrame:
    """
    Create OBI features from orderbook data using layered approach.

    For each timestamp:
    1. Create 40 layers within ±1% of midprice (20 above, 20 below)
    2. Calculate OBI features:
       - Layer 1: (a1-a2)/(a1+a2) where a1=volume above, a2=volume below
       - Layer 2: [(a1+b1)-(a2+b2)]/[(a1+b1)+(a2+b2)] (cumulative)
       - Continue for all layers

    Args:
        orderbook_df: Orderbook data with Price, Size, Time columns
        midprice_df: Midprice data with Time, MidPrice columns
        num_layers: Total number of layers (40 = 20 above + 20 below midprice)
        layer_width_pct: Width of each layer as percentage (0.01 = 1%)

    Returns:
        DataFrame with OBI features for each timestamp
    """
    obi_features = []

    print(f"Creating OBI features with {num_layers} layers...")

    for _, midprice_row in tqdm(midprice_df.iterrows(), total=len(midprice_df)):
        time_point = midprice_row["Time"]
        midprice = midprice_row["MidPrice"]

        # Get orderbook snapshot for this timestamp
        snapshot = orderbook_df[orderbook_df["Time"] == time_point]

        if len(snapshot) == 0:
            continue

        # Define layer boundaries within ±1% of midprice
        upper_bound = midprice * (1 + layer_width_pct)
        lower_bound = midprice * (1 - layer_width_pct)

        # Create layers within the ±1% range
        layer_size = (upper_bound - lower_bound) / num_layers

        # Initialize layer volumes
        layers_above = np.zeros(num_layers // 2)  # 20 layers above midprice
        layers_below = np.zeros(num_layers // 2)  # 20 layers below midprice

        # Assign orderbook entries to layers
        for _, row in snapshot.iterrows():
            price = row["Price"]
            size = row["Size"]

            if lower_bound <= price <= upper_bound:
                if price >= midprice:
                    # Above midprice
                    layer_idx = min(
                        int((price - midprice) / layer_size), num_layers // 2 - 1
                    )
                    layers_above[layer_idx] += size
                else:
                    # Below midprice
                    layer_idx = min(
                        int((midprice - price) / layer_size), num_layers // 2 - 1
                    )
                    layers_below[layer_idx] += size

        # Calculate OBI features
        obi_feature_row = {"Time": time_point, "MidPrice": midprice}

        # Individual layer OBI features
        for i in range(num_layers // 2):
            a_above = layers_above[i]
            a_below = layers_below[i]

            # Individual layer OBI: (a1-a2)/(a1+a2)
            if a_above + a_below > 0:
                obi_individual = (a_above - a_below) / (a_above + a_below)
            else:
                obi_individual = 0.0

            obi_feature_row[f"obi_layer_{i+1}"] = obi_individual

        # Cumulative layer OBI features
        for i in range(num_layers // 2):
            # Cumulative volumes up to layer i
            cum_above = np.sum(layers_above[: i + 1])
            cum_below = np.sum(layers_below[: i + 1])

            # Cumulative OBI: [(a1+...+ai)-(a2+...+ai)]/[(a1+...+ai)+(a2+...+ai)]
            if cum_above + cum_below > 0:
                obi_cumulative = (cum_above - cum_below) / (cum_above + cum_below)
            else:
                obi_cumulative = 0.0

            obi_feature_row[f"obi_cumulative_{i+1}"] = obi_cumulative

        obi_features.append(obi_feature_row)

    return pd.DataFrame(obi_features)


def create_price_layers(
    df: pd.DataFrame, num_layers: int = 40, layer_width_pct: float = 0.01
) -> pd.DataFrame:
    """
    Create price layers around mid price. Each layer is ±1% from mid price.
    Layer 0: mid_price ± 0.5%
    Layer 1: mid_price ± 1.5%
    ...
    Layer 39: mid_price ± 39.5%
    """
    df = df.copy()

    # Calculate which layer each trade belongs to
    price_deviation = (df["Price"] - df["MidPrice"]) / df["MidPrice"]

    # Assign layer based on absolute deviation
    abs_deviation = np.abs(price_deviation)
    df["Layer"] = np.floor(abs_deviation / layer_width_pct).astype(int)

    # Cap at maximum layer
    df["Layer"] = np.minimum(df["Layer"], num_layers - 1)

    # Add side information for layers (positive for above mid, negative for below)
    df["LayerSide"] = np.where(price_deviation >= 0, 1, -1)
    df["SignedLayer"] = df["Layer"] * df["LayerSide"]

    return df


def calculate_order_flow_features(
    trade_df: pd.DataFrame, time_window_minutes: int = 1
) -> pd.DataFrame:
    """
    Calculate order flow (OF) features from trade data.

    OF = past 1 minute buy size - sell size

    Args:
        trade_df: Trade data with Time, Size, Side columns
        time_window_minutes: Time window for order flow calculation (default: 1 minute)

    Returns:
        DataFrame with order flow features for each timestamp
    """
    print(
        f"Calculating order flow features with {time_window_minutes}-minute window..."
    )

    trade_df = trade_df.copy()
    trade_df["Time"] = pd.to_datetime(trade_df["Time"])
    trade_df = trade_df.sort_values("Time").reset_index(drop=True)

    # Create signed volume (buy = +, sell = -)
    trade_df["SignedVolume"] = np.where(
        trade_df["Side"].str.lower() == "buy", trade_df["Size"], -trade_df["Size"]
    )

    order_flow_features = []
    window_seconds = time_window_minutes * 60

    for i in tqdm(range(len(trade_df))):
        current_time = trade_df.iloc[i]["Time"]
        start_time = current_time - timedelta(seconds=window_seconds)

        # Get trades in the past window
        window_mask = (trade_df["Time"] >= start_time) & (
            trade_df["Time"] < current_time
        )
        window_trades = trade_df[window_mask]

        if len(window_trades) > 0:
            # Calculate order flow metrics
            total_order_flow = window_trades["SignedVolume"].sum()
            buy_volume = window_trades[window_trades["Side"].str.lower() == "buy"][
                "Size"
            ].sum()
            sell_volume = window_trades[window_trades["Side"].str.lower() == "sell"][
                "Size"
            ].sum()

            # Additional order flow metrics
            trade_count = len(window_trades)
            buy_count = len(window_trades[window_trades["Side"].str.lower() == "buy"])
            sell_count = len(window_trades[window_trades["Side"].str.lower() == "sell"])

            order_flow_features.append(
                {
                    "Time": current_time,
                    "order_flow": total_order_flow,
                    "buy_volume": buy_volume,
                    "sell_volume": sell_volume,
                    "net_volume": buy_volume - sell_volume,
                    "volume_ratio": buy_volume
                    / (sell_volume + 1e-8),  # Avoid division by zero
                    "trade_count": trade_count,
                    "buy_count": buy_count,
                    "sell_count": sell_count,
                    "buy_ratio": buy_count / (trade_count + 1e-8),
                }
            )
        else:
            # No trades in window
            order_flow_features.append(
                {
                    "Time": current_time,
                    "order_flow": 0.0,
                    "buy_volume": 0.0,
                    "sell_volume": 0.0,
                    "net_volume": 0.0,
                    "volume_ratio": 1.0,
                    "trade_count": 0,
                    "buy_count": 0,
                    "sell_count": 0,
                    "buy_ratio": 0.5,
                }
            )

    return pd.DataFrame(order_flow_features)


def create_layered_features(
    df: pd.DataFrame, lookback_window: int = 300
) -> pd.DataFrame:
    """
    Create features based on trading activity in different price layers
    """
    df = df.copy()

    # Initialize feature columns
    num_layers = df["Layer"].max() + 1

    # For each trade, calculate features from previous trades in the lookback window
    features = []

    print(f"Creating layered features with {num_layers} layers...")

    for i in tqdm(range(lookback_window, len(df))):
        current_time = df.iloc[i]["Time"]
        lookback_data = df.iloc[i - lookback_window : i]

        # Volume by layer
        layer_volumes = {}
        layer_counts = {}
        layer_buy_ratios = {}

        for layer in range(num_layers):
            layer_data = lookback_data[lookback_data["Layer"] == layer]

            if len(layer_data) > 0:
                layer_volumes[f"volume_layer_{layer}"] = layer_data["Size"].sum()
                layer_counts[f"count_layer_{layer}"] = len(layer_data)

                # Buy ratio in this layer
                buy_trades = layer_data[layer_data["Side"] == "buy"]
                layer_buy_ratios[f"buy_ratio_layer_{layer}"] = len(buy_trades) / len(
                    layer_data
                )
            else:
                layer_volumes[f"volume_layer_{layer}"] = 0.0
                layer_counts[f"count_layer_{layer}"] = 0
                layer_buy_ratios[f"buy_ratio_layer_{layer}"] = 0.5  # neutral

        # Aggregate features
        feature_row = {
            "index": i,
            "Time": current_time,
            "Price": df.iloc[i]["Price"],
            "Size": df.iloc[i]["Size"],
            "Side": df.iloc[i]["Side"],
            "MidPrice": df.iloc[i]["MidPrice"],
            "Layer": df.iloc[i]["Layer"],
            # Volume distribution across layers
            "total_volume": sum(layer_volumes.values()),
            "volume_concentration": max(layer_volumes.values())
            / (sum(layer_volumes.values()) + 1e-8),
            # Activity distribution
            "total_trades": sum(layer_counts.values()),
            "trade_concentration": max(layer_counts.values())
            / (sum(layer_counts.values()) + 1e-8),
            # Imbalance features
            "overall_buy_ratio": len(lookback_data[lookback_data["Side"] == "buy"])
            / len(lookback_data),
        }

        # Add individual layer features (top 10 layers only to keep manageable)
        for layer in range(min(10, num_layers)):
            feature_row[f"volume_layer_{layer}"] = layer_volumes.get(
                f"volume_layer_{layer}", 0.0
            )
            feature_row[f"count_layer_{layer}"] = layer_counts.get(
                f"count_layer_{layer}", 0
            )
            feature_row[f"buy_ratio_layer_{layer}"] = layer_buy_ratios.get(
                f"buy_ratio_layer_{layer}", 0.5
            )

        features.append(feature_row)

    return pd.DataFrame(features)


def create_multi_horizon_targets(
    df: pd.DataFrame, horizons: List[int] = None
) -> pd.DataFrame:
    """
    Create target variables for multiple prediction horizons (10s, 20s, ..., 200s)
    """
    if horizons is None:
        horizons = list(range(10, 210, 10))  # 10s, 20s, ..., 200s

    df = df.copy()

    print(f"Creating targets for horizons: {horizons} seconds")

    for horizon in horizons:
        # Initialize target columns
        df[f"future_side_{horizon}s"] = None
        df[f"future_size_{horizon}s"] = 0.0
        df[f"future_buy_volume_{horizon}s"] = 0.0
        df[f"future_sell_volume_{horizon}s"] = 0.0
        df[f"future_trade_count_{horizon}s"] = 0

        for i in range(len(df) - 1):
            current_time = df.iloc[i]["Time"]
            future_time = current_time + timedelta(seconds=horizon)

            # Find trades in the future window
            future_mask = (df["Time"] > current_time) & (df["Time"] <= future_time)
            future_trades = df[future_mask]

            if len(future_trades) > 0:
                # Next trade side and size
                next_trade = future_trades.iloc[0]
                df.loc[i, f"future_side_{horizon}s"] = next_trade["Side"]
                df.loc[i, f"future_size_{horizon}s"] = next_trade["Size"]

                # Aggregate statistics for the horizon
                buy_trades = future_trades[future_trades["Side"] == "buy"]
                sell_trades = future_trades[future_trades["Side"] == "sell"]

                df.loc[i, f"future_buy_volume_{horizon}s"] = buy_trades["Size"].sum()
                df.loc[i, f"future_sell_volume_{horizon}s"] = sell_trades["Size"].sum()
                df.loc[i, f"future_trade_count_{horizon}s"] = len(future_trades)

    return df


def bayesian_optimize_lasso(X_train, y_train, X_val, y_val, n_calls: int = 20) -> float:
    """
    Use Bayesian optimization to find optimal LASSO alpha parameter.

    Args:
        X_train, y_train: Training data
        X_val, y_val: Validation data
        n_calls: Number of optimization calls

    Returns:
        Optimal alpha value
    """
    if not BAYESIAN_OPT_AVAILABLE:
        print("Bayesian optimization not available, using default alpha=1.0")
        return 1.0

    # Define search space for LASSO alpha
    space = [Real(1e-6, 10.0, prior="log-uniform", name="alpha")]

    @use_named_args(space)
    def objective(alpha):
        """Objective function to minimize (negative R² score)"""
        try:
            model = Lasso(alpha=alpha, max_iter=2000, random_state=42)
            model.fit(X_train, y_train)
            y_pred = model.predict(X_val)
            r2 = r2_score(y_val, y_pred)
            return -r2  # Minimize negative R²
        except:
            return 1.0  # Return high error if fitting fails

    # Perform Bayesian optimization
    result = gp_minimize(objective, space, n_calls=n_calls, random_state=42)
    optimal_alpha = result.x[0]

    print(f"Optimal LASSO alpha found: {optimal_alpha:.6f}")
    return optimal_alpha


def bayesian_optimize_classifier(
    X_train, y_train, X_val, y_val, n_calls: int = 20
) -> Dict:
    """
    Use Bayesian optimization to find optimal classifier parameters.

    Returns:
        Dictionary with optimal parameters
    """
    if not BAYESIAN_OPT_AVAILABLE:
        print("Bayesian optimization not available, using default parameters")
        return {"C": 1.0, "max_iter": 1000}

    # Define search space
    space = [
        Real(1e-6, 100.0, prior="log-uniform", name="C"),
        Integer(500, 3000, name="max_iter"),
    ]

    @use_named_args(space)
    def objective(C, max_iter):
        """Objective function to minimize (negative accuracy)"""
        try:
            model = LogisticRegression(C=C, max_iter=max_iter, random_state=42)
            model.fit(X_train, y_train)
            y_pred = model.predict(X_val)
            accuracy = accuracy_score(y_val, y_pred)
            return -accuracy  # Minimize negative accuracy
        except:
            return 1.0  # Return high error if fitting fails

    # Perform Bayesian optimization
    result = gp_minimize(objective, space, n_calls=n_calls, random_state=42)
    optimal_params = {"C": result.x[0], "max_iter": result.x[1]}

    print(
        f"Optimal classifier parameters: C={optimal_params['C']:.6f}, max_iter={optimal_params['max_iter']}"
    )
    return optimal_params


def train_layered_models(
    features_df: pd.DataFrame, horizons: List[int] = None, use_bayesian_opt: bool = True
) -> Dict:
    """
    Train multiple models for different prediction horizons using LASSO regression and Bayesian optimization.
    """
    if horizons is None:
        horizons = list(range(10, 210, 10))

    # Prepare feature columns - include OBI and order flow features
    feature_cols = [
        col
        for col in features_df.columns
        if col.startswith(
            (
                "volume_",
                "count_",
                "buy_ratio_",
                "total_",
                "overall_",
                "obi_",
                "order_flow",
                "buy_volume",
                "sell_volume",
                "net_volume",
                "volume_ratio",
            )
        )
    ]

    print(
        f"Training models with {len(feature_cols)} features for {len(horizons)} horizons"
    )
    print(f"Using Bayesian optimization: {use_bayesian_opt and BAYESIAN_OPT_AVAILABLE}")

    X = features_df[feature_cols].fillna(0)

    # Scale features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    models = {"scaler": scaler, "feature_cols": feature_cols, "horizons": {}}

    for horizon in horizons:
        print(f"Training models for {horizon}s horizon...")

        # Side prediction (classification)
        side_col = f"future_side_{horizon}s"
        if side_col in features_df.columns:
            y_side = features_df[side_col].dropna()
            valid_indices = y_side.index
            X_side = X_scaled[valid_indices]

            if len(y_side) > 100:  # Minimum samples for training
                # Encode side labels
                le_side = LabelEncoder()
                y_side_encoded = le_side.fit_transform(y_side)

                X_train, X_val, y_train, y_val = train_test_split(
                    X_side, y_side_encoded, test_size=0.3, random_state=42
                )

                # Further split validation into validation and test
                X_val_split, X_test, y_val_split, y_test = train_test_split(
                    X_val, y_val, test_size=0.5, random_state=42
                )

                # Train side prediction models
                side_models = {}

                # Logistic Regression with Bayesian optimization
                if use_bayesian_opt and BAYESIAN_OPT_AVAILABLE:
                    optimal_params = bayesian_optimize_classifier(
                        X_train, y_train, X_val_split, y_val_split
                    )
                    lr_side = LogisticRegression(
                        C=optimal_params["C"],
                        max_iter=optimal_params["max_iter"],
                        random_state=42,
                    )
                else:
                    lr_side = LogisticRegression(random_state=42, max_iter=1000)

                lr_side.fit(X_train, y_train)
                lr_pred = lr_side.predict(X_test)
                lr_acc = accuracy_score(y_test, lr_pred)
                side_models["logistic"] = {"model": lr_side, "accuracy": lr_acc}

                # Random Forest
                rf_side = RandomForestClassifier(n_estimators=100, random_state=42)
                rf_side.fit(X_train, y_train)
                rf_pred = rf_side.predict(X_test)
                rf_acc = accuracy_score(y_test, rf_pred)
                side_models["random_forest"] = {"model": rf_side, "accuracy": rf_acc}

                models["horizons"][horizon] = {
                    "side_models": side_models,
                    "side_encoder": le_side,
                    "side_test_acc": {"logistic": lr_acc, "random_forest": rf_acc},
                }

        # Size prediction (regression) - conditional on side
        for side in ["buy", "sell"]:
            size_col = f"future_{side}_volume_{horizon}s"
            if size_col in features_df.columns:
                y_size = features_df[size_col].dropna()
                valid_indices = y_size.index
                X_size = X_scaled[valid_indices]

                print(
                    f"  {side} volume prediction: {len(y_size)} samples, mean={y_size.mean():.3f}, std={y_size.std():.3f}"
                )

                # Use log transformation for volume prediction to handle high variance
                # Add small constant to handle zeros
                y_size_log = np.log1p(y_size)  # log(1 + x) to handle zeros

                # Only predict if we have sufficient non-zero samples
                nonzero_mask = y_size > 0
                if nonzero_mask.sum() > 100:  # Increased minimum samples
                    X_size_filtered = X_size[nonzero_mask]
                    y_size_filtered = y_size_log[nonzero_mask]

                    # Check for sufficient variance in target
                    if y_size_filtered.std() > 0.01:  # Minimum variance threshold
                        X_train, X_val, y_train, y_val = train_test_split(
                            X_size_filtered,
                            y_size_filtered,
                            test_size=0.3,
                            random_state=42,
                        )

                        # Further split validation into validation and test
                        X_val_split, X_test, y_val_split, y_test = train_test_split(
                            X_val, y_val, test_size=0.5, random_state=42
                        )

                        # Train size prediction models
                        size_models = {}

                        # LASSO Regression with Bayesian optimization
                        if use_bayesian_opt and BAYESIAN_OPT_AVAILABLE:
                            optimal_alpha = bayesian_optimize_lasso(
                                X_train, y_train, X_val_split, y_val_split
                            )
                            lasso_size = Lasso(
                                alpha=optimal_alpha, max_iter=2000, random_state=42
                            )
                        else:
                            lasso_size = Lasso(
                                alpha=1.0, max_iter=2000, random_state=42
                            )

                        lasso_size.fit(X_train, y_train)
                        lasso_pred = lasso_size.predict(X_test)
                        lasso_r2 = r2_score(y_test, lasso_pred)
                        lasso_mse = mean_squared_error(y_test, lasso_pred)
                        size_models["lasso"] = {
                            "model": lasso_size,
                            "r2": lasso_r2,
                            "mse": lasso_mse,
                        }

                        # Random Forest with better parameters
                        rf_size = RandomForestRegressor(
                            n_estimators=50,
                            max_depth=10,
                            min_samples_split=10,
                            min_samples_leaf=5,
                            random_state=42,
                        )
                        rf_size.fit(X_train, y_train)
                        rf_pred = rf_size.predict(X_test)
                        rf_r2 = r2_score(y_test, rf_pred)
                        rf_mse = mean_squared_error(y_test, rf_pred)
                        size_models["random_forest"] = {
                            "model": rf_size,
                            "r2": rf_r2,
                            "mse": rf_mse,
                        }

                        if horizon not in models["horizons"]:
                            models["horizons"][horizon] = {}

                        models["horizons"][horizon][
                            f"{side}_volume_models"
                        ] = size_models
                        models["horizons"][horizon][f"{side}_volume_test_r2"] = {
                            "lasso": lasso_r2,
                            "random_forest": rf_r2,
                        }

                        print(
                            f"    {side} volume R²: LASSO={lasso_r2:.3f}, RF={rf_r2:.3f}"
                        )
                    else:
                        print(
                            f"    {side} volume: Insufficient variance in target (std={y_size_filtered.std():.6f})"
                        )
                else:
                    print(
                        f"    {side} volume: Insufficient non-zero samples ({nonzero_mask.sum()}/100 required)"
                    )

    return models


def get_available_trading_pairs(data_dir="../Data_fetching/Richard_data"):
    """Get all available trading pairs from the data directory"""
    data_path = Path(data_dir)
    trade_files = glob.glob(str(data_path / "*_trades_*.feather"))

    pairs = set()
    for file in trade_files:
        filename = Path(file).name
        # Extract pair name (e.g., "BTC_USD" from "BTC_USD_trades_1234.feather")
        pair = "_".join(filename.split("_")[:2])
        pairs.add(pair)

    print(f"Found {len(pairs)} trading pairs: {sorted(list(pairs))}")
    return sorted(list(pairs))


def analyze_obi_layered_prediction(
    pair: str,
    data_dir="../Data_fetching/Richard_data",
    sample_size: int = 15000,
    num_layers: int = 40,
    lookback_window: int = 300,
    horizons: List[int] = None,
    max_orderbook_files: int = 5,
    max_trade_files: int = 5,
    use_bayesian_opt: bool = True,
) -> Dict:
    """
    Complete OBI-based layered prediction analysis combining orderbook and trade data.

    This function:
    1. Loads both orderbook and trade data
    2. Calculates midprice from orderbook data
    3. Creates OBI features from layered orderbook data (40 layers, ±1% around midprice)
    4. Calculates order flow features from trade data (past 1-minute buy-sell imbalance)
    5. Trains LASSO and Random Forest models with Bayesian optimization
    6. Predicts future trade side and volume across multiple horizons

    Args:
        pair: Trading pair (e.g., "BTC_USD")
        data_dir: Directory containing data files
        sample_size: Maximum number of samples to use
        num_layers: Number of price layers (40 = 20 above + 20 below midprice)
        lookback_window: Window for traditional layered features
        horizons: Prediction horizons in seconds
        max_orderbook_files: Maximum orderbook files to load
        max_trade_files: Maximum trade files to load
        use_bayesian_opt: Whether to use Bayesian optimization for hyperparameters

    Returns:
        Dictionary containing models, features, and analysis results
    """
    if horizons is None:
        horizons = list(range(10, 210, 10))  # 10s to 200s

    print(f"\n{'='*80}")
    print(f"OBI LAYERED PREDICTION ANALYSIS FOR {pair}")
    print(f"Layers: {num_layers} (±1% around midprice)")
    print(f"Lookback window: {lookback_window} trades")
    print(f"Prediction horizons: {horizons} seconds")
    print(f"Bayesian optimization: {use_bayesian_opt and BAYESIAN_OPT_AVAILABLE}")
    print(f"{'='*80}")

    try:
        # Load orderbook data
        print("Loading orderbook data...")
        orderbook_df = load_orderbook_data(pair, data_dir, max_orderbook_files)
        print(f"Loaded {len(orderbook_df)} orderbook entries")

        # Sample orderbook data if too large
        if len(orderbook_df) > sample_size * 10:  # More generous for orderbook
            print(
                f"Sampling {sample_size * 10} orderbook entries from {len(orderbook_df)} total"
            )
            orderbook_df = (
                orderbook_df.sample(n=sample_size * 10, random_state=42)
                .sort_values("Time")
                .reset_index(drop=True)
            )

        # Calculate midprice from orderbook
        print("Calculating midprice from orderbook data...")
        midprice_df = calculate_midprice_from_orderbook(orderbook_df)
        print(f"Generated {len(midprice_df)} midprice points")

        # Create OBI features
        print("Creating OBI features from layered orderbook data...")
        obi_features_df = create_obi_layers_from_orderbook(
            orderbook_df, midprice_df, num_layers
        )
        print(f"Generated {len(obi_features_df)} OBI feature rows")

        # Load trade data
        print("Loading trade data...")
        trade_df = load_trading_pair_data(pair, data_dir)
        print(f"Loaded {len(trade_df)} trades")

        # Sample trade data if too large
        if len(trade_df) > sample_size:
            print(f"Sampling {sample_size} trades from {len(trade_df)} total")
            trade_df = (
                trade_df.sample(n=sample_size, random_state=42)
                .sort_values("Time")
                .reset_index(drop=True)
            )

        # Calculate order flow features
        print("Calculating order flow features...")
        order_flow_df = calculate_order_flow_features(trade_df)
        print(f"Generated {len(order_flow_df)} order flow feature rows")

        return {
            "status": "partial",
            "obi_features": obi_features_df,
            "order_flow": order_flow_df,
        }

    except Exception as e:
        print(f"Error in OBI layered analysis: {str(e)}")
        import traceback

        traceback.print_exc()
        return None


def analyze_layered_prediction(
    pair: str,
    data_dir="../Data_fetching/Richard_data",
    sample_size: int = 15000,
    num_layers: int = 40,
    lookback_window: int = 300,
    horizons: List[int] = None,
) -> Dict:
    """
    Complete layered prediction analysis for a trading pair
    """
    if horizons is None:
        horizons = list(range(10, 210, 10))  # 10s to 200s

    print(f"\n{'='*80}")
    print(f"LAYERED PREDICTION ANALYSIS FOR {pair}")
    print(f"Layers: {num_layers} (±1% each)")
    print(f"Lookback window: {lookback_window} trades")
    print(f"Prediction horizons: {horizons} seconds")
    print(f"{'='*80}")

    try:
        # Load data
        df = load_trading_pair_data(pair, data_dir)
        print(f"Loaded {len(df)} trades")

        # Sample if too large
        if len(df) > sample_size:
            print(f"Sampling {sample_size} trades from {len(df)} total")
            df = (
                df.sample(n=sample_size, random_state=42)
                .sort_values("Time")
                .reset_index(drop=True)
            )

        # Calculate mid price and layers
        print("Calculating mid price and price layers...")
        df = calculate_mid_price_rolling(df)
        df = create_price_layers(df, num_layers)

        # Create layered features
        print("Creating layered features...")
        features_df = create_layered_features(df, lookback_window)

        # Create multi-horizon targets
        print("Creating multi-horizon targets...")
        features_df = create_multi_horizon_targets(features_df, horizons)

        # Train models
        print("Training layered prediction models...")
        models = train_layered_models(features_df, horizons)

        return {
            "pair": pair,
            "models": models,
            "features_df": features_df,
            "num_layers": num_layers,
            "horizons": horizons,
            "data_size": len(df),
            "feature_size": len(features_df),
        }

    except Exception as e:
        print(f"Error in layered analysis: {str(e)}")
        import traceback

        traceback.print_exc()
        return None


def predict_future_trades(
    models: Dict, features_df: pd.DataFrame, horizon: int = 60
) -> Dict:
    """
    Make predictions for future trades using trained layered models
    """
    if horizon not in models["horizons"]:
        return {"error": f"No models trained for {horizon}s horizon"}

    horizon_models = models["horizons"][horizon]
    scaler = models["scaler"]
    feature_cols = models["feature_cols"]

    # Get the most recent features
    recent_features = features_df[feature_cols].iloc[-1:].fillna(0)
    X_scaled = scaler.transform(recent_features)

    predictions = {"horizon": horizon, "predictions": {}}

    # Side prediction
    if "side_models" in horizon_models:
        side_encoder = horizon_models["side_encoder"]

        for model_name, model_info in horizon_models["side_models"].items():
            model = model_info["model"]
            side_pred_encoded = model.predict(X_scaled)[0]
            side_pred = side_encoder.inverse_transform([side_pred_encoded])[0]
            side_prob = model.predict_proba(X_scaled)[0].max()

            predictions["predictions"][f"side_{model_name}"] = {
                "predicted_side": side_pred,
                "confidence": side_prob,
                "accuracy": model_info["accuracy"],
            }

    # Volume predictions (conditional on side)
    for side in ["buy", "sell"]:
        volume_key = f"{side}_volume_models"
        if volume_key in horizon_models:
            for model_name, model_info in horizon_models[volume_key].items():
                model = model_info["model"]
                volume_pred = model.predict(X_scaled)[0]

                predictions["predictions"][f"{side}_volume_{model_name}"] = {
                    "predicted_volume": max(0, volume_pred),
                    "r2_score": model_info["r2"],
                    "mse": model_info["mse"],
                }

    return predictions


def visualize_layered_results(result: Dict):
    """Create comprehensive visualizations for layered prediction results"""

    if not result or "models" not in result:
        print("No valid results to visualize")
        return

    models = result["models"]
    horizons = result["horizons"]

    # Create subplots
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f"Layered Prediction Analysis - {result['pair']}", fontsize=16)

    # 1. Side prediction accuracy across horizons
    ax1 = axes[0, 0]
    horizon_list = []
    lr_accs = []
    rf_accs = []

    for horizon in sorted(horizons):
        if (
            horizon in models["horizons"]
            and "side_test_acc" in models["horizons"][horizon]
        ):
            horizon_list.append(horizon)
            lr_accs.append(
                models["horizons"][horizon]["side_test_acc"].get("logistic", 0)
            )
            rf_accs.append(
                models["horizons"][horizon]["side_test_acc"].get("random_forest", 0)
            )

    if horizon_list:
        ax1.plot(horizon_list, lr_accs, "o-", label="Logistic Regression", color="blue")
        ax1.plot(horizon_list, rf_accs, "s-", label="Random Forest", color="green")
        ax1.set_xlabel("Prediction Horizon (seconds)")
        ax1.set_ylabel("Side Prediction Accuracy")
        ax1.set_title("Side Prediction Performance")
        ax1.legend()
        ax1.grid(True, alpha=0.3)

    # 2. Buy volume prediction R² across horizons
    ax2 = axes[0, 1]
    buy_lr_r2 = []
    buy_rf_r2 = []

    for horizon in sorted(horizons):
        if (
            horizon in models["horizons"]
            and "buy_volume_test_r2" in models["horizons"][horizon]
        ):
            buy_lr_r2.append(
                models["horizons"][horizon]["buy_volume_test_r2"].get("linear", 0)
            )
            buy_rf_r2.append(
                models["horizons"][horizon]["buy_volume_test_r2"].get(
                    "random_forest", 0
                )
            )

    if buy_lr_r2:
        ax2.plot(
            horizon_list[: len(buy_lr_r2)],
            buy_lr_r2,
            "o-",
            label="Linear Regression",
            color="blue",
        )
        ax2.plot(
            horizon_list[: len(buy_rf_r2)],
            buy_rf_r2,
            "s-",
            label="Random Forest",
            color="green",
        )
        ax2.set_xlabel("Prediction Horizon (seconds)")
        ax2.set_ylabel("Buy Volume R² Score")
        ax2.set_title("Buy Volume Prediction Performance")
        ax2.legend()
        ax2.grid(True, alpha=0.3)

    # 3. Sell volume prediction R² across horizons
    ax3 = axes[0, 2]
    sell_lr_r2 = []
    sell_rf_r2 = []

    for horizon in sorted(horizons):
        if (
            horizon in models["horizons"]
            and "sell_volume_test_r2" in models["horizons"][horizon]
        ):
            sell_lr_r2.append(
                models["horizons"][horizon]["sell_volume_test_r2"].get("linear", 0)
            )
            sell_rf_r2.append(
                models["horizons"][horizon]["sell_volume_test_r2"].get(
                    "random_forest", 0
                )
            )

    if sell_lr_r2:
        ax3.plot(
            horizon_list[: len(sell_lr_r2)],
            sell_lr_r2,
            "o-",
            label="Linear Regression",
            color="blue",
        )
        ax3.plot(
            horizon_list[: len(sell_rf_r2)],
            sell_rf_r2,
            "s-",
            label="Random Forest",
            color="green",
        )
        ax3.set_xlabel("Prediction Horizon (seconds)")
        ax3.set_ylabel("Sell Volume R² Score")
        ax3.set_title("Sell Volume Prediction Performance")
        ax3.legend()
        ax3.grid(True, alpha=0.3)

    # 4. Layer distribution analysis
    ax4 = axes[1, 0]
    if "features_df" in result:
        features_df = result["features_df"]
        layer_counts = []
        for layer in range(min(10, result["num_layers"])):
            count = (features_df["Layer"] == layer).sum()
            layer_counts.append(count)

        ax4.bar(range(len(layer_counts)), layer_counts, color="skyblue", alpha=0.7)
        ax4.set_xlabel("Price Layer")
        ax4.set_ylabel("Number of Trades")
        ax4.set_title("Trade Distribution Across Layers (Top 10)")
        ax4.grid(True, alpha=0.3)

    # 5. Model comparison heatmap
    ax5 = axes[1, 1]
    if horizon_list and lr_accs:
        comparison_data = np.array([lr_accs, rf_accs])
        im = ax5.imshow(comparison_data, cmap="RdYlGn", aspect="auto", vmin=0, vmax=1)
        ax5.set_xticks(range(len(horizon_list)))
        ax5.set_xticklabels([f"{h}s" for h in horizon_list])
        ax5.set_yticks([0, 1])
        ax5.set_yticklabels(["Logistic Reg", "Random Forest"])
        ax5.set_title("Side Prediction Accuracy Heatmap")

        # Add text annotations
        for i in range(2):
            for j in range(len(horizon_list)):
                ax5.text(
                    j,
                    i,
                    f"{comparison_data[i, j]:.2f}",
                    ha="center",
                    va="center",
                    color="black",
                    fontsize=8,
                )

        plt.colorbar(im, ax=ax5)

    # 6. Summary statistics
    ax6 = axes[1, 2]
    summary_text = f"""
    LAYERED PREDICTION SUMMARY

    Trading Pair: {result['pair']}
    Number of Layers: {result['num_layers']}
    Data Size: {result['data_size']:,} trades
    Feature Size: {result['feature_size']:,} samples

    Prediction Horizons: {len(horizons)}
    ({min(horizons)}s to {max(horizons)}s)

    Best Side Accuracy:
    LR: {max(lr_accs) if lr_accs else 0:.1%}
    RF: {max(rf_accs) if rf_accs else 0:.1%}

    Best Volume R²:
    Buy: {max(buy_rf_r2) if buy_rf_r2 else 0:.3f}
    Sell: {max(sell_rf_r2) if sell_rf_r2 else 0:.3f}
    """

    ax6.text(
        0.05,
        0.95,
        summary_text,
        transform=ax6.transAxes,
        fontsize=10,
        verticalalignment="top",
        bbox=dict(boxstyle="round", facecolor="lightblue"),
    )
    ax6.set_title("Analysis Summary")
    ax6.axis("off")

    plt.tight_layout()
    plt.savefig(
        f'layered_prediction_{result["pair"]}.png', dpi=300, bbox_inches="tight"
    )
    plt.show()


def main():
    """Main function to run OBI-based layered prediction analysis"""

    print("🏗️ OBI LAYERED TRADING PREDICTION ANALYSIS")
    print("=" * 80)
    print("Features:")
    print("- OBI features from 40 layered orderbook data (±1% around midprice)")
    print("- Order flow features from past 1-minute buy-sell imbalance")
    print("- LASSO regression with L1 penalty")
    print("- Bayesian optimization for hyperparameter tuning")
    print("=" * 80)

    # Get available trading pairs
    available_pairs = get_available_trading_pairs()
    print(f"Found {len(available_pairs)} trading pairs: {available_pairs}")

    # Analyze first few pairs as examples
    pairs_to_analyze = available_pairs[:3]  # Analyze first 3 pairs for demo

    all_results = []

    for pair in pairs_to_analyze:
        print(f"\n🔄 Analyzing {pair} with OBI features...")

        # Use the new OBI-based analysis
        result = analyze_obi_layered_prediction(
            pair=pair,
            sample_size=5000,  # Smaller sample for faster processing
            num_layers=40,
            lookback_window=200,
            horizons=[10, 30, 60, 120, 200],  # Selected horizons
            max_orderbook_files=3,  # Limit files for demo
            max_trade_files=3,
            use_bayesian_opt=True,
        )

        if result and result.get("status") == "partial":
            print(f"✅ {pair}: OBI feature extraction complete")
            all_results.append(result)

            # Display sample features
            if "obi_features" in result:
                obi_df = result["obi_features"]
                print(f"OBI features shape: {obi_df.shape}")
                print("Sample OBI features:")
                obi_cols = [col for col in obi_df.columns if col.startswith("obi_")]
                print(obi_df[obi_cols[:5]].head())

            if "order_flow" in result:
                of_df = result["order_flow"]
                print(f"Order flow features shape: {of_df.shape}")
                print("Sample order flow features:")
                of_cols = ["order_flow", "net_volume", "volume_ratio", "buy_ratio"]
                available_of_cols = [col for col in of_cols if col in of_df.columns]
                print(of_df[available_of_cols].head())
        else:
            print(f"❌ {pair}: Analysis failed")

    # Fallback to traditional analysis for comparison
    print(f"\n🔄 Running traditional layered analysis for comparison...")
    traditional_result = analyze_layered_prediction(
        pair=pairs_to_analyze[0] if pairs_to_analyze else "BTC_USD",
        sample_size=3000,
        num_layers=40,
        lookback_window=200,
        horizons=[30, 60, 120],
    )

    if traditional_result:
        print(f"✅ Traditional analysis complete")
        all_results.append(traditional_result)

    if all_results:
        print(f"\n📊 OBI LAYERED PREDICTION ANALYSIS SUMMARY")
        print("=" * 80)
        print(f"Successfully processed {len(all_results)} analysis runs")

        # Count different types of results
        obi_results = [r for r in all_results if r.get("status") == "partial"]
        traditional_results = [r for r in all_results if "models" in r]

        print(f"OBI feature extractions: {len(obi_results)}")
        print(f"Traditional model training: {len(traditional_results)}")

        # Summary of OBI features
        if obi_results:
            print(f"\n🎯 OBI FEATURES SUMMARY:")
            for result in obi_results:
                if "obi_features" in result:
                    obi_df = result["obi_features"]
                    obi_cols = [col for col in obi_df.columns if col.startswith("obi_")]
                    print(
                        f"  - Generated {len(obi_cols)} OBI features from {len(obi_df)} timestamps"
                    )

                if "order_flow" in result:
                    of_df = result["order_flow"]
                    of_cols = [
                        col
                        for col in of_df.columns
                        if col.startswith(("order_flow", "net_volume", "volume_ratio"))
                    ]
                    print(
                        f"  - Generated {len(of_cols)} order flow features from {len(of_df)} timestamps"
                    )

        # Summary of traditional results
        if traditional_results:
            print(f"\n🎯 MODEL PERFORMANCE SUMMARY:")
            summary_data = []
            for result in traditional_results:
                if "models" in result and "horizons" in result:
                    models = result["models"]
                    for horizon in result["horizons"]:
                        if horizon in models["horizons"]:
                            horizon_models = models["horizons"][horizon]

                            summary_row = {
                                "pair": result["pair"],
                                "horizon": horizon,
                                "num_layers": result["num_layers"],
                                "data_size": result["data_size"],
                            }

                            # Add model performance metrics (updated for LASSO)
                            if "side_test_acc" in horizon_models:
                                summary_row["side_acc_lr"] = horizon_models[
                                    "side_test_acc"
                                ].get("logistic", 0)
                                summary_row["side_acc_rf"] = horizon_models[
                                    "side_test_acc"
                                ].get("random_forest", 0)

                            if "buy_volume_test_r2" in horizon_models:
                                summary_row["buy_vol_r2_lasso"] = horizon_models[
                                    "buy_volume_test_r2"
                                ].get("lasso", 0)
                                summary_row["buy_vol_r2_rf"] = horizon_models[
                                    "buy_volume_test_r2"
                                ].get("random_forest", 0)

                            if "sell_volume_test_r2" in horizon_models:
                                summary_row["sell_vol_r2_lasso"] = horizon_models[
                                    "sell_volume_test_r2"
                                ].get("lasso", 0)
                                summary_row["sell_vol_r2_rf"] = horizon_models[
                                    "sell_volume_test_r2"
                                ].get("random_forest", 0)

                            summary_data.append(summary_row)

            if summary_data:
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_csv("obi_layered_prediction_results.csv", index=False)
                print(f"Results saved to 'obi_layered_prediction_results.csv'")

                # Print best performers
                if "side_acc_rf" in summary_df.columns:
                    best_side = summary_df.loc[summary_df["side_acc_rf"].idxmax()]
                    print(
                        f"🏆 Best Side Prediction: {best_side['pair']} at {best_side['horizon']}s - {best_side['side_acc_rf']:.1%}"
                    )

                if "buy_vol_r2_lasso" in summary_df.columns:
                    best_buy_vol = summary_df.loc[
                        summary_df["buy_vol_r2_lasso"].idxmax()
                    ]
                    print(
                        f"🏆 Best Buy Volume (LASSO): {best_buy_vol['pair']} at {best_buy_vol['horizon']}s - R²={best_buy_vol['buy_vol_r2_lasso']:.3f}"
                    )

        print(f"\n🎉 ANALYSIS COMPLETE!")
        print("Key improvements implemented:")
        print("✅ OBI features from 40 layered orderbook data (±1% around midprice)")
        print("✅ Order flow features from past 1-minute buy-sell imbalance")
        print("✅ LASSO regression with L1 penalty for feature selection")
        print("✅ Bayesian optimization for hyperparameter tuning")
        print("✅ Support for both orderbook and trade data analysis")

        return all_results

    return None


if __name__ == "__main__":
    results = main()

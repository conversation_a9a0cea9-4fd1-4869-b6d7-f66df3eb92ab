import pandas as pd
import numpy as np
import glob
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression, Lasso, LassoCV
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import (
    accuracy_score,
    mean_squared_error,
    r2_score,
)
import warnings
from typing import Dict, List, Tuple
from datetime import timedelta, datetime
from tqdm import tqdm
import json

# Bayesian optimization imports
try:
    from skopt import gp_minimize
    from skopt.space import Real, Integer, Categorical
    from skopt.utils import use_named_args

    BAYESIAN_OPT_AVAILABLE = True
except ImportError:
    print(
        "Warning: scikit-optimize not available. Install with: pip install scikit-optimize"
    )
    BAYESIAN_OPT_AVAILABLE = False

warnings.filterwarnings("ignore")


def install_dependencies():
    """Install required dependencies for Bayesian optimization"""
    try:
        import subprocess
        import sys

        print("Installing scikit-optimize for Bayesian optimization...")
        subprocess.check_call(
            [sys.executable, "-m", "pip", "install", "scikit-optimize"]
        )
        print("✅ scikit-optimize installed successfully")

        # Try importing again
        global BAYESIAN_OPT_AVAILABLE
        try:
            from skopt import gp_minimize
            from skopt.space import Real, Integer, Categorical
            from skopt.utils import use_named_args

            BAYESIAN_OPT_AVAILABLE = True
            print("✅ Bayesian optimization is now available")
        except ImportError:
            print("❌ Failed to import scikit-optimize after installation")

    except Exception as e:
        print(f"❌ Failed to install scikit-optimize: {e}")
        print("Please install manually: pip install scikit-optimize")


def load_trading_pair_data(pair: str, data_dir="../Data_fetching/Richard_data"):
    """Load and combine all trade data for a specific trading pair"""
    data_path = Path(data_dir)
    trade_files = glob.glob(str(data_path / f"{pair}_trades_*.feather"))

    if not trade_files:
        raise FileNotFoundError(f"No trade files found for {pair} in {data_dir}")

    print(f"Found {len(trade_files)} trade files for {pair}")

    # Load and combine all files
    dfs = []
    for file in trade_files:
        df = pd.read_feather(file)
        dfs.append(df)

    combined_df = pd.concat(dfs, ignore_index=True)

    # Convert time to datetime and sort
    combined_df["Time"] = pd.to_datetime(combined_df["Time"], errors="coerce")
    combined_df = combined_df.dropna(subset=["Time"])
    combined_df = combined_df.sort_values("Time").reset_index(drop=True)

    return combined_df


def load_orderbook_data(
    pair: str, data_dir="../Data_fetching/Richard_data", max_files: int = None
):
    """Load and combine orderbook data for a specific trading pair"""
    data_path = Path(data_dir)
    orderbook_files = glob.glob(str(data_path / f"{pair}_orderbook_*.feather"))

    if not orderbook_files:
        raise FileNotFoundError(f"No orderbook files found for {pair} in {data_dir}")

    # Limit files if specified
    if max_files:
        orderbook_files = orderbook_files[:max_files]

    print(f"Found {len(orderbook_files)} orderbook files for {pair}")

    # Load and combine all files
    dfs = []
    for file in orderbook_files:
        df = pd.read_feather(file)
        dfs.append(df)

    combined_df = pd.concat(dfs, ignore_index=True)

    # Convert time to datetime and sort
    combined_df["Time"] = pd.to_datetime(combined_df["Time"], errors="coerce")
    combined_df = combined_df.dropna(subset=["Time"])
    combined_df = combined_df.sort_values("Time").reset_index(drop=True)

    return combined_df


def calculate_midprice_from_orderbook(
    df: pd.DataFrame, window_size: int = 100
) -> pd.DataFrame:
    """Calculate midprice from orderbook data by finding best bid and ask at each timestamp"""
    df = df.copy()

    # Group by time to get snapshots
    snapshots = []

    for time_point, group in df.groupby("Time"):
        # Sort by price to identify bids (lower prices) and asks (higher prices)
        group_sorted = group.sort_values("Price")

        # Find the midpoint price to separate bids and asks
        mid_idx = len(group_sorted) // 2

        # Best bid (highest price below midpoint) and best ask (lowest price above midpoint)
        bids = group_sorted.iloc[:mid_idx]
        asks = group_sorted.iloc[mid_idx:]

        if len(bids) > 0 and len(asks) > 0:
            best_bid = bids["Price"].max()
            best_ask = asks["Price"].min()
            midprice = (best_bid + best_ask) / 2

            snapshots.append(
                {
                    "Time": time_point,
                    "MidPrice": midprice,
                    "BestBid": best_bid,
                    "BestAsk": best_ask,
                    "Spread": best_ask - best_bid,
                }
            )

    midprice_df = pd.DataFrame(snapshots)

    # Apply rolling window smoothing
    midprice_df["MidPrice"] = (
        midprice_df["MidPrice"]
        .rolling(window=min(window_size, len(midprice_df)), center=True)
        .median()
    )
    midprice_df["MidPrice"] = (
        midprice_df["MidPrice"].fillna(method="bfill").fillna(method="ffill")
    )

    return midprice_df


def calculate_mid_price_rolling(
    df: pd.DataFrame, window_size: int = 100
) -> pd.DataFrame:
    """Calculate rolling mid price for layering"""
    df = df.copy()
    df["MidPrice"] = df["Price"].rolling(window=window_size, center=True).median()
    df["MidPrice"] = df["MidPrice"].fillna(method="bfill").fillna(method="ffill")
    return df


def create_obi_layers_from_orderbook(
    orderbook_df: pd.DataFrame,
    midprice_df: pd.DataFrame,
    num_layers: int = 40,
    layer_width_pct: float = 0.01,
) -> pd.DataFrame:
    """
    Create OBI features from orderbook data using layered approach.

    For each timestamp:
    1. Create 40 layers within ±1% of midprice (20 above, 20 below)
    2. Calculate OBI features:
       - Layer 1: (a1-a2)/(a1+a2) where a1=volume above, a2=volume below
       - Layer 2: [(a1+b1)-(a2+b2)]/[(a1+b1)+(a2+b2)] (cumulative)
       - Continue for all layers

    Args:
        orderbook_df: Orderbook data with Price, Size, Time columns
        midprice_df: Midprice data with Time, MidPrice columns
        num_layers: Total number of layers (40 = 20 above + 20 below midprice)
        layer_width_pct: Width of each layer as percentage (0.01 = 1%)

    Returns:
        DataFrame with OBI features for each timestamp
    """
    obi_features = []

    print(f"Creating OBI features with {num_layers} layers...")

    for _, midprice_row in tqdm(midprice_df.iterrows(), total=len(midprice_df)):
        time_point = midprice_row["Time"]
        midprice = midprice_row["MidPrice"]

        # Get orderbook snapshot for this timestamp
        snapshot = orderbook_df[orderbook_df["Time"] == time_point]

        if len(snapshot) == 0:
            continue

        # Define layer boundaries within ±1% of midprice
        upper_bound = midprice * (1 + layer_width_pct)
        lower_bound = midprice * (1 - layer_width_pct)

        # Create layers within the ±1% range
        layer_size = (upper_bound - lower_bound) / num_layers

        # Initialize layer volumes
        layers_above = np.zeros(num_layers // 2)  # 20 layers above midprice
        layers_below = np.zeros(num_layers // 2)  # 20 layers below midprice

        # Assign orderbook entries to layers
        for _, row in snapshot.iterrows():
            price = row["Price"]
            size = row["Size"]

            if lower_bound <= price <= upper_bound:
                if price >= midprice:
                    # Above midprice
                    layer_idx = min(
                        int((price - midprice) / layer_size), num_layers // 2 - 1
                    )
                    layers_above[layer_idx] += size
                else:
                    # Below midprice
                    layer_idx = min(
                        int((midprice - price) / layer_size), num_layers // 2 - 1
                    )
                    layers_below[layer_idx] += size

        # Calculate OBI features
        obi_feature_row = {"Time": time_point, "MidPrice": midprice}

        # Individual layer OBI features
        for i in range(num_layers // 2):
            a_above = layers_above[i]
            a_below = layers_below[i]

            # Individual layer OBI: (a1-a2)/(a1+a2)
            if a_above + a_below > 0:
                obi_individual = (a_above - a_below) / (a_above + a_below)
            else:
                obi_individual = 0.0

            obi_feature_row[f"obi_layer_{i+1}"] = obi_individual

        # Cumulative layer OBI features
        for i in range(num_layers // 2):
            # Cumulative volumes up to layer i
            cum_above = np.sum(layers_above[: i + 1])
            cum_below = np.sum(layers_below[: i + 1])

            # Cumulative OBI: [(a1+...+ai)-(a2+...+ai)]/[(a1+...+ai)+(a2+...+ai)]
            if cum_above + cum_below > 0:
                obi_cumulative = (cum_above - cum_below) / (cum_above + cum_below)
            else:
                obi_cumulative = 0.0

            obi_feature_row[f"obi_cumulative_{i+1}"] = obi_cumulative

        obi_features.append(obi_feature_row)

    return pd.DataFrame(obi_features)


def create_price_layers(
    df: pd.DataFrame, num_layers: int = 40, layer_width_pct: float = 0.01
) -> pd.DataFrame:
    """
    Create price layers around mid price. Each layer is ±1% from mid price.
    Layer 0: mid_price ± 0.5%
    Layer 1: mid_price ± 1.5%
    ...
    Layer 39: mid_price ± 39.5%
    """
    df = df.copy()

    # Calculate which layer each trade belongs to
    price_deviation = (df["Price"] - df["MidPrice"]) / df["MidPrice"]

    # Assign layer based on absolute deviation
    abs_deviation = np.abs(price_deviation)
    df["Layer"] = np.floor(abs_deviation / layer_width_pct).astype(int)

    # Cap at maximum layer
    df["Layer"] = np.minimum(df["Layer"], num_layers - 1)

    # Add side information for layers (positive for above mid, negative for below)
    df["LayerSide"] = np.where(price_deviation >= 0, 1, -1)
    df["SignedLayer"] = df["Layer"] * df["LayerSide"]

    return df


def calculate_order_flow_features(
    trade_df: pd.DataFrame, time_window_minutes: int = 1
) -> pd.DataFrame:
    """
    Calculate order flow (OF) features from trade data.

    OF = past 1 minute buy size - sell size

    Args:
        trade_df: Trade data with Time, Size, Side columns
        time_window_minutes: Time window for order flow calculation (default: 1 minute)

    Returns:
        DataFrame with order flow features for each timestamp
    """
    print(
        f"Calculating order flow features with {time_window_minutes}-minute window..."
    )

    trade_df = trade_df.copy()
    trade_df["Time"] = pd.to_datetime(trade_df["Time"])
    trade_df = trade_df.sort_values("Time").reset_index(drop=True)

    # Create signed volume (buy = +, sell = -)
    trade_df["SignedVolume"] = np.where(
        trade_df["Side"].str.lower() == "buy", trade_df["Size"], -trade_df["Size"]
    )

    order_flow_features = []
    window_seconds = time_window_minutes * 60

    for i in tqdm(range(len(trade_df))):
        current_time = trade_df.iloc[i]["Time"]
        start_time = current_time - timedelta(seconds=window_seconds)

        # Get trades in the past window
        window_mask = (trade_df["Time"] >= start_time) & (
            trade_df["Time"] < current_time
        )
        window_trades = trade_df[window_mask]

        if len(window_trades) > 0:
            # Calculate order flow metrics
            total_order_flow = window_trades["SignedVolume"].sum()
            buy_volume = window_trades[window_trades["Side"].str.lower() == "buy"][
                "Size"
            ].sum()
            sell_volume = window_trades[window_trades["Side"].str.lower() == "sell"][
                "Size"
            ].sum()

            # Additional order flow metrics
            trade_count = len(window_trades)
            buy_count = len(window_trades[window_trades["Side"].str.lower() == "buy"])
            sell_count = len(window_trades[window_trades["Side"].str.lower() == "sell"])

            order_flow_features.append(
                {
                    "Time": current_time,
                    "order_flow": total_order_flow,
                    "buy_volume": buy_volume,
                    "sell_volume": sell_volume,
                    "net_volume": buy_volume - sell_volume,
                    "volume_ratio": buy_volume
                    / (sell_volume + 1e-8),  # Avoid division by zero
                    "trade_count": trade_count,
                    "buy_count": buy_count,
                    "sell_count": sell_count,
                    "buy_ratio": buy_count / (trade_count + 1e-8),
                }
            )
        else:
            # No trades in window
            order_flow_features.append(
                {
                    "Time": current_time,
                    "order_flow": 0.0,
                    "buy_volume": 0.0,
                    "sell_volume": 0.0,
                    "net_volume": 0.0,
                    "volume_ratio": 1.0,
                    "trade_count": 0,
                    "buy_count": 0,
                    "sell_count": 0,
                    "buy_ratio": 0.5,
                }
            )

    return pd.DataFrame(order_flow_features)


def create_layered_features(
    df: pd.DataFrame, lookback_window: int = 300
) -> pd.DataFrame:
    """
    Create features based on trading activity in different price layers
    """
    df = df.copy()

    # Initialize feature columns
    num_layers = df["Layer"].max() + 1

    # For each trade, calculate features from previous trades in the lookback window
    features = []

    print(f"Creating layered features with {num_layers} layers...")

    for i in tqdm(range(lookback_window, len(df))):
        current_time = df.iloc[i]["Time"]
        lookback_data = df.iloc[i - lookback_window : i]

        # Volume by layer
        layer_volumes = {}
        layer_counts = {}
        layer_buy_ratios = {}

        for layer in range(num_layers):
            layer_data = lookback_data[lookback_data["Layer"] == layer]

            if len(layer_data) > 0:
                layer_volumes[f"volume_layer_{layer}"] = layer_data["Size"].sum()
                layer_counts[f"count_layer_{layer}"] = len(layer_data)

                # Buy ratio in this layer
                buy_trades = layer_data[layer_data["Side"] == "buy"]
                layer_buy_ratios[f"buy_ratio_layer_{layer}"] = len(buy_trades) / len(
                    layer_data
                )
            else:
                layer_volumes[f"volume_layer_{layer}"] = 0.0
                layer_counts[f"count_layer_{layer}"] = 0
                layer_buy_ratios[f"buy_ratio_layer_{layer}"] = 0.5  # neutral

        # Aggregate features
        feature_row = {
            "index": i,
            "Time": current_time,
            "Price": df.iloc[i]["Price"],
            "Size": df.iloc[i]["Size"],
            "Side": df.iloc[i]["Side"],
            "MidPrice": df.iloc[i]["MidPrice"],
            "Layer": df.iloc[i]["Layer"],
            # Volume distribution across layers
            "total_volume": sum(layer_volumes.values()),
            "volume_concentration": max(layer_volumes.values())
            / (sum(layer_volumes.values()) + 1e-8),
            # Activity distribution
            "total_trades": sum(layer_counts.values()),
            "trade_concentration": max(layer_counts.values())
            / (sum(layer_counts.values()) + 1e-8),
            # Imbalance features
            "overall_buy_ratio": len(lookback_data[lookback_data["Side"] == "buy"])
            / len(lookback_data),
        }

        # Add individual layer features (top 10 layers only to keep manageable)
        for layer in range(min(10, num_layers)):
            feature_row[f"volume_layer_{layer}"] = layer_volumes.get(
                f"volume_layer_{layer}", 0.0
            )
            feature_row[f"count_layer_{layer}"] = layer_counts.get(
                f"count_layer_{layer}", 0
            )
            feature_row[f"buy_ratio_layer_{layer}"] = layer_buy_ratios.get(
                f"buy_ratio_layer_{layer}", 0.5
            )

        features.append(feature_row)

    return pd.DataFrame(features)


def create_multi_horizon_targets(
    df: pd.DataFrame, horizons: List[int] = None
) -> pd.DataFrame:
    """
    Create target variables for multiple prediction horizons (10s, 20s, ..., 200s)
    """
    if horizons is None:
        horizons = list(range(10, 210, 10))  # 10s, 20s, ..., 200s

    df = df.copy()

    print(f"Creating targets for horizons: {horizons} seconds")

    for horizon in horizons:
        # Initialize target columns
        df[f"future_side_{horizon}s"] = None
        df[f"future_size_{horizon}s"] = 0.0
        df[f"future_buy_volume_{horizon}s"] = 0.0
        df[f"future_sell_volume_{horizon}s"] = 0.0
        df[f"future_trade_count_{horizon}s"] = 0

        for i in range(len(df) - 1):
            current_time = df.iloc[i]["Time"]
            future_time = current_time + timedelta(seconds=horizon)

            # Find trades in the future window
            future_mask = (df["Time"] > current_time) & (df["Time"] <= future_time)
            future_trades = df[future_mask]

            if len(future_trades) > 0:
                # Next trade side and size
                next_trade = future_trades.iloc[0]
                df.loc[i, f"future_side_{horizon}s"] = next_trade["Side"]
                df.loc[i, f"future_size_{horizon}s"] = next_trade["Size"]

                # Aggregate statistics for the horizon
                buy_trades = future_trades[future_trades["Side"] == "buy"]
                sell_trades = future_trades[future_trades["Side"] == "sell"]

                df.loc[i, f"future_buy_volume_{horizon}s"] = buy_trades["Size"].sum()
                df.loc[i, f"future_sell_volume_{horizon}s"] = sell_trades["Size"].sum()
                df.loc[i, f"future_trade_count_{horizon}s"] = len(future_trades)

    return df


def bayesian_optimize_lasso(X_train, y_train, X_val, y_val, n_calls: int = 20) -> float:
    """
    Use Bayesian optimization to find optimal LASSO alpha parameter.

    Args:
        X_train, y_train: Training data
        X_val, y_val: Validation data
        n_calls: Number of optimization calls

    Returns:
        Optimal alpha value
    """
    if not BAYESIAN_OPT_AVAILABLE:
        print("Bayesian optimization not available, using default alpha=1.0")
        return 1.0

    # Define search space for LASSO alpha
    space = [Real(1e-6, 10.0, prior="log-uniform", name="alpha")]

    @use_named_args(space)
    def objective(alpha):
        """Objective function to minimize (negative R² score)"""
        try:
            model = Lasso(alpha=alpha, max_iter=2000, random_state=42)
            model.fit(X_train, y_train)
            y_pred = model.predict(X_val)
            r2 = r2_score(y_val, y_pred)
            return -r2  # Minimize negative R²
        except:
            return 1.0  # Return high error if fitting fails

    # Perform Bayesian optimization
    result = gp_minimize(objective, space, n_calls=n_calls, random_state=42)
    optimal_alpha = result.x[0]

    print(f"Optimal LASSO alpha found: {optimal_alpha:.6f}")
    return optimal_alpha


def bayesian_optimize_classifier(
    X_train, y_train, X_val, y_val, n_calls: int = 20
) -> Dict:
    """
    Use Bayesian optimization to find optimal classifier parameters.

    Returns:
        Dictionary with optimal parameters
    """
    if not BAYESIAN_OPT_AVAILABLE:
        print("Bayesian optimization not available, using default parameters")
        return {"C": 1.0, "max_iter": 1000}

    # Define search space
    space = [
        Real(1e-6, 100.0, prior="log-uniform", name="C"),
        Integer(500, 3000, name="max_iter"),
    ]

    @use_named_args(space)
    def objective(C, max_iter):
        """Objective function to minimize (negative accuracy)"""
        try:
            model = LogisticRegression(C=C, max_iter=max_iter, random_state=42)
            model.fit(X_train, y_train)
            y_pred = model.predict(X_val)
            accuracy = accuracy_score(y_val, y_pred)
            return -accuracy  # Minimize negative accuracy
        except:
            return 1.0  # Return high error if fitting fails

    # Perform Bayesian optimization
    result = gp_minimize(objective, space, n_calls=n_calls, random_state=42)
    optimal_params = {"C": result.x[0], "max_iter": result.x[1]}

    print(
        f"Optimal classifier parameters: C={optimal_params['C']:.6f}, max_iter={optimal_params['max_iter']}"
    )
    return optimal_params


def train_layered_models(
    features_df: pd.DataFrame, horizons: List[int] = None, use_bayesian_opt: bool = True
) -> Dict:
    """
    Train multiple models for different prediction horizons using LASSO regression and Bayesian optimization.
    """
    if horizons is None:
        horizons = list(range(10, 210, 10))

    # Prepare feature columns - include OBI and order flow features
    feature_cols = [
        col
        for col in features_df.columns
        if col.startswith(
            (
                "volume_",
                "count_",
                "buy_ratio_",
                "total_",
                "overall_",
                "obi_",
                "order_flow",
                "buy_volume",
                "sell_volume",
                "net_volume",
                "volume_ratio",
            )
        )
    ]

    print(
        f"Training models with {len(feature_cols)} features for {len(horizons)} horizons"
    )
    print(f"Using Bayesian optimization: {use_bayesian_opt and BAYESIAN_OPT_AVAILABLE}")

    X = features_df[feature_cols].fillna(0)

    # Scale features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    models = {"scaler": scaler, "feature_cols": feature_cols, "horizons": {}}

    for horizon in horizons:
        print(f"Training models for {horizon}s horizon...")

        # Side prediction (classification)
        side_col = f"future_side_{horizon}s"
        if side_col in features_df.columns:
            y_side = features_df[side_col].dropna()
            valid_indices = y_side.index
            X_side = X_scaled[valid_indices]

            if len(y_side) > 100:  # Minimum samples for training
                # Encode side labels
                le_side = LabelEncoder()
                y_side_encoded = le_side.fit_transform(y_side)

                X_train, X_val, y_train, y_val = train_test_split(
                    X_side, y_side_encoded, test_size=0.3, random_state=42
                )

                # Further split validation into validation and test
                X_val_split, X_test, y_val_split, y_test = train_test_split(
                    X_val, y_val, test_size=0.5, random_state=42
                )

                # Train side prediction models
                side_models = {}

                # Logistic Regression with Bayesian optimization
                if use_bayesian_opt and BAYESIAN_OPT_AVAILABLE:
                    optimal_params = bayesian_optimize_classifier(
                        X_train, y_train, X_val_split, y_val_split
                    )
                    lr_side = LogisticRegression(
                        C=optimal_params["C"],
                        max_iter=optimal_params["max_iter"],
                        random_state=42,
                    )
                else:
                    lr_side = LogisticRegression(random_state=42, max_iter=1000)

                lr_side.fit(X_train, y_train)
                lr_pred = lr_side.predict(X_test)
                lr_acc = accuracy_score(y_test, lr_pred)
                side_models["logistic"] = {"model": lr_side, "accuracy": lr_acc}

                # Random Forest
                rf_side = RandomForestClassifier(n_estimators=100, random_state=42)
                rf_side.fit(X_train, y_train)
                rf_pred = rf_side.predict(X_test)
                rf_acc = accuracy_score(y_test, rf_pred)
                side_models["random_forest"] = {"model": rf_side, "accuracy": rf_acc}

                models["horizons"][horizon] = {
                    "side_models": side_models,
                    "side_encoder": le_side,
                    "side_test_acc": {"logistic": lr_acc, "random_forest": rf_acc},
                }

        # Size prediction (regression) - conditional on side
        for side in ["buy", "sell"]:
            size_col = f"future_{side}_volume_{horizon}s"
            if size_col in features_df.columns:
                y_size = features_df[size_col].dropna()
                valid_indices = y_size.index
                X_size = X_scaled[valid_indices]

                print(
                    f"  {side} volume prediction: {len(y_size)} samples, mean={y_size.mean():.3f}, std={y_size.std():.3f}"
                )

                # Use log transformation for volume prediction to handle high variance
                # Add small constant to handle zeros
                y_size_log = np.log1p(y_size)  # log(1 + x) to handle zeros

                # Only predict if we have sufficient non-zero samples
                nonzero_mask = y_size > 0
                if nonzero_mask.sum() > 100:  # Increased minimum samples
                    X_size_filtered = X_size[nonzero_mask]
                    y_size_filtered = y_size_log[nonzero_mask]

                    # Check for sufficient variance in target
                    if y_size_filtered.std() > 0.01:  # Minimum variance threshold
                        X_train, X_val, y_train, y_val = train_test_split(
                            X_size_filtered,
                            y_size_filtered,
                            test_size=0.3,
                            random_state=42,
                        )

                        # Further split validation into validation and test
                        X_val_split, X_test, y_val_split, y_test = train_test_split(
                            X_val, y_val, test_size=0.5, random_state=42
                        )

                        # Train size prediction models
                        size_models = {}

                        # LASSO Regression with Bayesian optimization
                        if use_bayesian_opt and BAYESIAN_OPT_AVAILABLE:
                            optimal_alpha = bayesian_optimize_lasso(
                                X_train, y_train, X_val_split, y_val_split
                            )
                            lasso_size = Lasso(
                                alpha=optimal_alpha, max_iter=2000, random_state=42
                            )
                        else:
                            lasso_size = Lasso(
                                alpha=1.0, max_iter=2000, random_state=42
                            )

                        lasso_size.fit(X_train, y_train)
                        lasso_pred = lasso_size.predict(X_test)
                        lasso_r2 = r2_score(y_test, lasso_pred)
                        lasso_mse = mean_squared_error(y_test, lasso_pred)
                        size_models["lasso"] = {
                            "model": lasso_size,
                            "r2": lasso_r2,
                            "mse": lasso_mse,
                        }

                        # Random Forest with better parameters
                        rf_size = RandomForestRegressor(
                            n_estimators=50,
                            max_depth=10,
                            min_samples_split=10,
                            min_samples_leaf=5,
                            random_state=42,
                        )
                        rf_size.fit(X_train, y_train)
                        rf_pred = rf_size.predict(X_test)
                        rf_r2 = r2_score(y_test, rf_pred)
                        rf_mse = mean_squared_error(y_test, rf_pred)
                        size_models["random_forest"] = {
                            "model": rf_size,
                            "r2": rf_r2,
                            "mse": rf_mse,
                        }

                        if horizon not in models["horizons"]:
                            models["horizons"][horizon] = {}

                        models["horizons"][horizon][
                            f"{side}_volume_models"
                        ] = size_models
                        models["horizons"][horizon][f"{side}_volume_test_r2"] = {
                            "lasso": lasso_r2,
                            "random_forest": rf_r2,
                        }

                        print(
                            f"    {side} volume R²: LASSO={lasso_r2:.3f}, RF={rf_r2:.3f}"
                        )
                    else:
                        print(
                            f"    {side} volume: Insufficient variance in target (std={y_size_filtered.std():.6f})"
                        )
                else:
                    print(
                        f"    {side} volume: Insufficient non-zero samples ({nonzero_mask.sum()}/100 required)"
                    )

    return models


def get_available_trading_pairs(data_dir="../Data_fetching/Richard_data"):
    """Get all available trading pairs from the data directory"""
    data_path = Path(data_dir)
    trade_files = glob.glob(str(data_path / "*_trades_*.feather"))

    pairs = set()
    for file in trade_files:
        filename = Path(file).name
        # Extract pair name (e.g., "BTC_USD" from "BTC_USD_trades_1234.feather")
        pair = "_".join(filename.split("_")[:2])
        pairs.add(pair)

    print(f"Found {len(pairs)} trading pairs: {sorted(list(pairs))}")
    return sorted(list(pairs))


def analyze_obi_layered_prediction(
    pair: str,
    data_dir="../Data_fetching/Richard_data",
    sample_size: int = 15000,
    num_layers: int = 40,
    lookback_window: int = 300,
    horizons: List[int] = None,
    max_orderbook_files: int = 5,
    max_trade_files: int = 5,
    use_bayesian_opt: bool = True,
) -> Dict:
    """
    Complete OBI-based layered prediction analysis combining orderbook and trade data.

    This function:
    1. Loads both orderbook and trade data
    2. Calculates midprice from orderbook data
    3. Creates OBI features from layered orderbook data (40 layers, ±1% around midprice)
    4. Calculates order flow features from trade data (past 1-minute buy-sell imbalance)
    5. Trains LASSO and Random Forest models with Bayesian optimization
    6. Predicts future trade side and volume across multiple horizons

    Args:
        pair: Trading pair (e.g., "BTC_USD")
        data_dir: Directory containing data files
        sample_size: Maximum number of samples to use
        num_layers: Number of price layers (40 = 20 above + 20 below midprice)
        lookback_window: Window for traditional layered features
        horizons: Prediction horizons in seconds
        max_orderbook_files: Maximum orderbook files to load
        max_trade_files: Maximum trade files to load
        use_bayesian_opt: Whether to use Bayesian optimization for hyperparameters

    Returns:
        Dictionary containing models, features, and analysis results
    """
    if horizons is None:
        horizons = list(range(10, 210, 10))  # 10s to 200s

    print(f"\n{'='*80}")
    print(f"OBI LAYERED PREDICTION ANALYSIS FOR {pair}")
    print(f"Layers: {num_layers} (±1% around midprice)")
    print(f"Lookback window: {lookback_window} trades")
    print(f"Prediction horizons: {horizons} seconds")
    print(f"Bayesian optimization: {use_bayesian_opt and BAYESIAN_OPT_AVAILABLE}")
    print(f"{'='*80}")

    try:
        # Load orderbook data
        print("Loading orderbook data...")
        orderbook_df = load_orderbook_data(pair, data_dir, max_orderbook_files)
        print(f"Loaded {len(orderbook_df)} orderbook entries")

        # Sample orderbook data if too large
        if len(orderbook_df) > sample_size * 10:  # More generous for orderbook
            print(
                f"Sampling {sample_size * 10} orderbook entries from {len(orderbook_df)} total"
            )
            orderbook_df = (
                orderbook_df.sample(n=sample_size * 10, random_state=42)
                .sort_values("Time")
                .reset_index(drop=True)
            )

        # Calculate midprice from orderbook
        print("Calculating midprice from orderbook data...")
        midprice_df = calculate_midprice_from_orderbook(orderbook_df)
        print(f"Generated {len(midprice_df)} midprice points")

        # Create OBI features
        print("Creating OBI features from layered orderbook data...")
        obi_features_df = create_obi_layers_from_orderbook(
            orderbook_df, midprice_df, num_layers
        )
        print(f"Generated {len(obi_features_df)} OBI feature rows")

        # Load trade data
        print("Loading trade data...")
        trade_df = load_trading_pair_data(pair, data_dir)
        print(f"Loaded {len(trade_df)} trades")

        # Sample trade data if too large
        if len(trade_df) > sample_size:
            print(f"Sampling {sample_size} trades from {len(trade_df)} total")
            trade_df = (
                trade_df.sample(n=sample_size, random_state=42)
                .sort_values("Time")
                .reset_index(drop=True)
            )

        # Calculate order flow features
        print("Calculating order flow features...")
        order_flow_df = calculate_order_flow_features(trade_df)
        print(f"Generated {len(order_flow_df)} order flow feature rows")

        return {
            "status": "partial",
            "obi_features": obi_features_df,
            "order_flow": order_flow_df,
        }

    except Exception as e:
        print(f"Error in OBI layered analysis: {str(e)}")
        import traceback

        traceback.print_exc()
        return None


def analyze_layered_prediction(
    pair: str,
    data_dir="../Data_fetching/Richard_data",
    sample_size: int = 15000,
    num_layers: int = 40,
    lookback_window: int = 300,
    horizons: List[int] = None,
) -> Dict:
    """
    Complete layered prediction analysis for a trading pair
    """
    if horizons is None:
        horizons = list(range(10, 210, 10))  # 10s to 200s

    print(f"\n{'='*80}")
    print(f"LAYERED PREDICTION ANALYSIS FOR {pair}")
    print(f"Layers: {num_layers} (±1% each)")
    print(f"Lookback window: {lookback_window} trades")
    print(f"Prediction horizons: {horizons} seconds")
    print(f"{'='*80}")

    try:
        # Load data
        df = load_trading_pair_data(pair, data_dir)
        print(f"Loaded {len(df)} trades")

        # Sample if too large
        if len(df) > sample_size:
            print(f"Sampling {sample_size} trades from {len(df)} total")
            df = (
                df.sample(n=sample_size, random_state=42)
                .sort_values("Time")
                .reset_index(drop=True)
            )

        # Calculate mid price and layers
        print("Calculating mid price and price layers...")
        df = calculate_mid_price_rolling(df)
        df = create_price_layers(df, num_layers)

        # Create layered features
        print("Creating layered features...")
        features_df = create_layered_features(df, lookback_window)

        # Create multi-horizon targets
        print("Creating multi-horizon targets...")
        features_df = create_multi_horizon_targets(features_df, horizons)

        # Train models
        print("Training layered prediction models...")
        models = train_layered_models(features_df, horizons)

        return {
            "pair": pair,
            "models": models,
            "features_df": features_df,
            "num_layers": num_layers,
            "horizons": horizons,
            "data_size": len(df),
            "feature_size": len(features_df),
        }

    except Exception as e:
        print(f"Error in layered analysis: {str(e)}")
        import traceback

        traceback.print_exc()
        return None


def predict_future_trades(
    models: Dict, features_df: pd.DataFrame, horizon: int = 60
) -> Dict:
    """
    Make predictions for future trades using trained layered models
    """
    if horizon not in models["horizons"]:
        return {"error": f"No models trained for {horizon}s horizon"}

    horizon_models = models["horizons"][horizon]
    scaler = models["scaler"]
    feature_cols = models["feature_cols"]

    # Get the most recent features
    recent_features = features_df[feature_cols].iloc[-1:].fillna(0)
    X_scaled = scaler.transform(recent_features)

    predictions = {"horizon": horizon, "predictions": {}}

    # Side prediction
    if "side_models" in horizon_models:
        side_encoder = horizon_models["side_encoder"]

        for model_name, model_info in horizon_models["side_models"].items():
            model = model_info["model"]
            side_pred_encoded = model.predict(X_scaled)[0]
            side_pred = side_encoder.inverse_transform([side_pred_encoded])[0]
            side_prob = model.predict_proba(X_scaled)[0].max()

            predictions["predictions"][f"side_{model_name}"] = {
                "predicted_side": side_pred,
                "confidence": side_prob,
                "accuracy": model_info["accuracy"],
            }

    # Volume predictions (conditional on side)
    for side in ["buy", "sell"]:
        volume_key = f"{side}_volume_models"
        if volume_key in horizon_models:
            for model_name, model_info in horizon_models[volume_key].items():
                model = model_info["model"]
                volume_pred = model.predict(X_scaled)[0]

                predictions["predictions"][f"{side}_volume_{model_name}"] = {
                    "predicted_volume": max(0, volume_pred),
                    "r2_score": model_info["r2"],
                    "mse": model_info["mse"],
                }

    return predictions


def print_coin_prediction_results(results: List[Dict], save_to_file: bool = True):
    """
    Print detailed prediction results for each coin in terminal format.

    Args:
        results: List of analysis results for different coins
        save_to_file: Whether to save results to text file
    """
    print("\n" + "=" * 100)
    print("🪙 DETAILED PREDICTION RESULTS FOR ALL COINS")
    print("=" * 100)

    output_lines = []
    output_lines.append("=" * 100)
    output_lines.append("🪙 DETAILED PREDICTION RESULTS FOR ALL COINS")
    output_lines.append("=" * 100)

    # Separate OBI results and traditional results
    obi_results = [r for r in results if r.get("status") == "partial"]
    traditional_results = [r for r in results if "models" in r and "pair" in r]

    # Print OBI Feature Results
    if obi_results:
        print("\n📊 OBI FEATURE EXTRACTION RESULTS")
        print("-" * 60)
        output_lines.append("\n📊 OBI FEATURE EXTRACTION RESULTS")
        output_lines.append("-" * 60)

        for i, result in enumerate(obi_results, 1):
            pair_info = f"Coin #{i}"
            print(f"\n{pair_info}")
            output_lines.append(f"\n{pair_info}")

            if "obi_features" in result:
                obi_df = result["obi_features"]
                obi_cols = [col for col in obi_df.columns if col.startswith("obi_")]

                print(f"  📈 OBI Features:")
                print(f"    - Total timestamps: {len(obi_df):,}")
                print(f"    - OBI feature columns: {len(obi_cols)}")
                print(
                    f"    - Time range: {obi_df['Time'].min()} to {obi_df['Time'].max()}"
                )

                output_lines.extend(
                    [
                        f"  📈 OBI Features:",
                        f"    - Total timestamps: {len(obi_df):,}",
                        f"    - OBI feature columns: {len(obi_cols)}",
                        f"    - Time range: {obi_df['Time'].min()} to {obi_df['Time'].max()}",
                    ]
                )

                # Show sample OBI statistics
                if len(obi_df) > 0:
                    print(f"  📊 Sample OBI Statistics:")
                    output_lines.append(f"  📊 Sample OBI Statistics:")

                    for col in obi_cols[:5]:  # Show first 5 OBI features
                        values = obi_df[col].dropna()
                        if len(values) > 0:
                            mean_val = values.mean()
                            std_val = values.std()
                            min_val = values.min()
                            max_val = values.max()

                            print(f"    {col}:")
                            print(f"      Mean: {mean_val:8.4f} | Std: {std_val:8.4f}")
                            print(f"      Range: [{min_val:8.4f}, {max_val:8.4f}]")

                            output_lines.extend(
                                [
                                    f"    {col}:",
                                    f"      Mean: {mean_val:8.4f} | Std: {std_val:8.4f}",
                                    f"      Range: [{min_val:8.4f}, {max_val:8.4f}]",
                                ]
                            )

            if "order_flow" in result:
                of_df = result["order_flow"]
                of_cols = ["order_flow", "net_volume", "volume_ratio", "buy_ratio"]
                available_cols = [col for col in of_cols if col in of_df.columns]

                print(f"  📈 Order Flow Features:")
                print(f"    - Total timestamps: {len(of_df):,}")
                print(f"    - Available features: {available_cols}")

                output_lines.extend(
                    [
                        f"  📈 Order Flow Features:",
                        f"    - Total timestamps: {len(of_df):,}",
                        f"    - Available features: {available_cols}",
                    ]
                )

                if len(of_df) > 0:
                    print(f"  📊 Order Flow Statistics:")
                    output_lines.append(f"  📊 Order Flow Statistics:")

                    for col in available_cols:
                        values = of_df[col].dropna()
                        if len(values) > 0:
                            mean_val = values.mean()
                            std_val = values.std()
                            min_val = values.min()
                            max_val = values.max()

                            print(f"    {col}:")
                            print(f"      Mean: {mean_val:8.4f} | Std: {std_val:8.4f}")
                            print(f"      Range: [{min_val:8.4f}, {max_val:8.4f}]")

                            output_lines.extend(
                                [
                                    f"    {col}:",
                                    f"      Mean: {mean_val:8.4f} | Std: {std_val:8.4f}",
                                    f"      Range: [{min_val:8.4f}, {max_val:8.4f}]",
                                ]
                            )

    # Print Traditional Model Results
    if traditional_results:
        print("\n🤖 MODEL PREDICTION RESULTS")
        print("-" * 60)
        output_lines.append("\n🤖 MODEL PREDICTION RESULTS")
        output_lines.append("-" * 60)

        for result in traditional_results:
            pair = result.get("pair", "Unknown")
            print(f"\n💰 {pair.upper()}")
            print("=" * 50)
            output_lines.append(f"\n💰 {pair.upper()}")
            output_lines.append("=" * 50)

            # Basic info
            print(f"📊 Dataset Info:")
            print(f"  - Data size: {result.get('data_size', 'N/A'):,} trades")
            print(f"  - Feature size: {result.get('feature_size', 'N/A'):,} samples")
            print(f"  - Layers: {result.get('num_layers', 'N/A')}")
            print(f"  - Horizons: {result.get('horizons', 'N/A')}")

            output_lines.extend(
                [
                    f"📊 Dataset Info:",
                    f"  - Data size: {result.get('data_size', 'N/A'):,} trades",
                    f"  - Feature size: {result.get('feature_size', 'N/A'):,} samples",
                    f"  - Layers: {result.get('num_layers', 'N/A')}",
                    f"  - Horizons: {result.get('horizons', 'N/A')}",
                ]
            )

            if "models" in result:
                models = result["models"]
                horizons = result.get("horizons", [])

                print(f"\n🎯 Prediction Performance:")
                output_lines.append(f"\n🎯 Prediction Performance:")

                # Create performance table
                performance_data = []

                for horizon in sorted(horizons):
                    if horizon in models["horizons"]:
                        horizon_models = models["horizons"][horizon]

                        row_data = {"Horizon": f"{horizon}s"}

                        # Side prediction accuracy
                        if "side_test_acc" in horizon_models:
                            lr_acc = horizon_models["side_test_acc"].get("logistic", 0)
                            rf_acc = horizon_models["side_test_acc"].get(
                                "random_forest", 0
                            )
                            row_data["Side_LR"] = f"{lr_acc:.1%}"
                            row_data["Side_RF"] = f"{rf_acc:.1%}"

                        # Volume prediction R²
                        if "buy_volume_test_r2" in horizon_models:
                            lasso_r2 = horizon_models["buy_volume_test_r2"].get(
                                "lasso", 0
                            )
                            rf_r2 = horizon_models["buy_volume_test_r2"].get(
                                "random_forest", 0
                            )
                            row_data["Buy_Vol_LASSO"] = f"{lasso_r2:.3f}"
                            row_data["Buy_Vol_RF"] = f"{rf_r2:.3f}"

                        if "sell_volume_test_r2" in horizon_models:
                            lasso_r2 = horizon_models["sell_volume_test_r2"].get(
                                "lasso", 0
                            )
                            rf_r2 = horizon_models["sell_volume_test_r2"].get(
                                "random_forest", 0
                            )
                            row_data["Sell_Vol_LASSO"] = f"{lasso_r2:.3f}"
                            row_data["Sell_Vol_RF"] = f"{rf_r2:.3f}"

                        performance_data.append(row_data)

                # Print performance table
                if performance_data:
                    df_perf = pd.DataFrame(performance_data)
                    print(f"\n  Performance Table:")
                    print(f"  {'-' * 80}")
                    print(
                        f"  {'Horizon':<8} {'Side LR':<8} {'Side RF':<8} {'Buy LASSO':<10} {'Buy RF':<8} {'Sell LASSO':<11} {'Sell RF':<8}"
                    )
                    print(f"  {'-' * 80}")

                    output_lines.extend(
                        [
                            f"\n  Performance Table:",
                            f"  {'-' * 80}",
                            f"  {'Horizon':<8} {'Side LR':<8} {'Side RF':<8} {'Buy LASSO':<10} {'Buy RF':<8} {'Sell LASSO':<11} {'Sell RF':<8}",
                            f"  {'-' * 80}",
                        ]
                    )

                    for _, row in df_perf.iterrows():
                        horizon = row.get("Horizon", "N/A")
                        side_lr = row.get("Side_LR", "N/A")
                        side_rf = row.get("Side_RF", "N/A")
                        buy_lasso = row.get("Buy_Vol_LASSO", "N/A")
                        buy_rf = row.get("Buy_Vol_RF", "N/A")
                        sell_lasso = row.get("Sell_Vol_LASSO", "N/A")
                        sell_rf = row.get("Sell_Vol_RF", "N/A")

                        print(
                            f"  {horizon:<8} {side_lr:<8} {side_rf:<8} {buy_lasso:<10} {buy_rf:<8} {sell_lasso:<11} {sell_rf:<8}"
                        )
                        output_lines.append(
                            f"  {horizon:<8} {side_lr:<8} {side_rf:<8} {buy_lasso:<10} {buy_rf:<8} {sell_lasso:<11} {sell_rf:<8}"
                        )

                # Find best performance
                print(f"\n🏆 Best Performance:")
                output_lines.append(f"\n🏆 Best Performance:")

                best_side_acc = 0
                best_side_horizon = "N/A"
                best_vol_r2 = 0
                best_vol_horizon = "N/A"

                for horizon in sorted(horizons):
                    if horizon in models["horizons"]:
                        horizon_models = models["horizons"][horizon]

                        # Check side accuracy
                        if "side_test_acc" in horizon_models:
                            rf_acc = horizon_models["side_test_acc"].get(
                                "random_forest", 0
                            )
                            if rf_acc > best_side_acc:
                                best_side_acc = rf_acc
                                best_side_horizon = f"{horizon}s"

                        # Check volume R²
                        if "buy_volume_test_r2" in horizon_models:
                            lasso_r2 = horizon_models["buy_volume_test_r2"].get(
                                "lasso", 0
                            )
                            if lasso_r2 > best_vol_r2:
                                best_vol_r2 = lasso_r2
                                best_vol_horizon = f"{horizon}s"

                print(
                    f"  - Best Side Prediction: {best_side_acc:.1%} at {best_side_horizon}"
                )
                print(
                    f"  - Best Volume Prediction: R² = {best_vol_r2:.3f} at {best_vol_horizon}"
                )

                output_lines.extend(
                    [
                        f"  - Best Side Prediction: {best_side_acc:.1%} at {best_side_horizon}",
                        f"  - Best Volume Prediction: R² = {best_vol_r2:.3f} at {best_vol_horizon}",
                    ]
                )

    # Save to file if requested
    if save_to_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"modeling/coin_prediction_results_{timestamp}.txt"

        with open(filename, "w") as f:
            f.write("\n".join(output_lines))

        print(f"\n💾 Results saved to: {filename}")

    print("\n" + "=" * 100)


def visualize_layered_results(result: Dict):
    """Create comprehensive visualizations for layered prediction results"""

    if not result or "models" not in result:
        print("No valid results to visualize")
        return

    models = result["models"]
    horizons = result["horizons"]

    # Create subplots
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f"Layered Prediction Analysis - {result['pair']}", fontsize=16)

    # 1. Side prediction accuracy across horizons
    ax1 = axes[0, 0]
    horizon_list = []
    lr_accs = []
    rf_accs = []

    for horizon in sorted(horizons):
        if (
            horizon in models["horizons"]
            and "side_test_acc" in models["horizons"][horizon]
        ):
            horizon_list.append(horizon)
            lr_accs.append(
                models["horizons"][horizon]["side_test_acc"].get("logistic", 0)
            )
            rf_accs.append(
                models["horizons"][horizon]["side_test_acc"].get("random_forest", 0)
            )

    if horizon_list:
        ax1.plot(horizon_list, lr_accs, "o-", label="Logistic Regression", color="blue")
        ax1.plot(horizon_list, rf_accs, "s-", label="Random Forest", color="green")
        ax1.set_xlabel("Prediction Horizon (seconds)")
        ax1.set_ylabel("Side Prediction Accuracy")
        ax1.set_title("Side Prediction Performance")
        ax1.legend()
        ax1.grid(True, alpha=0.3)

    # 2. Buy volume prediction R² across horizons
    ax2 = axes[0, 1]
    buy_lr_r2 = []
    buy_rf_r2 = []

    for horizon in sorted(horizons):
        if (
            horizon in models["horizons"]
            and "buy_volume_test_r2" in models["horizons"][horizon]
        ):
            buy_lr_r2.append(
                models["horizons"][horizon]["buy_volume_test_r2"].get("linear", 0)
            )
            buy_rf_r2.append(
                models["horizons"][horizon]["buy_volume_test_r2"].get(
                    "random_forest", 0
                )
            )

    if buy_lr_r2:
        ax2.plot(
            horizon_list[: len(buy_lr_r2)],
            buy_lr_r2,
            "o-",
            label="Linear Regression",
            color="blue",
        )
        ax2.plot(
            horizon_list[: len(buy_rf_r2)],
            buy_rf_r2,
            "s-",
            label="Random Forest",
            color="green",
        )
        ax2.set_xlabel("Prediction Horizon (seconds)")
        ax2.set_ylabel("Buy Volume R² Score")
        ax2.set_title("Buy Volume Prediction Performance")
        ax2.legend()
        ax2.grid(True, alpha=0.3)

    # 3. Sell volume prediction R² across horizons
    ax3 = axes[0, 2]
    sell_lr_r2 = []
    sell_rf_r2 = []

    for horizon in sorted(horizons):
        if (
            horizon in models["horizons"]
            and "sell_volume_test_r2" in models["horizons"][horizon]
        ):
            sell_lr_r2.append(
                models["horizons"][horizon]["sell_volume_test_r2"].get("linear", 0)
            )
            sell_rf_r2.append(
                models["horizons"][horizon]["sell_volume_test_r2"].get(
                    "random_forest", 0
                )
            )

    if sell_lr_r2:
        ax3.plot(
            horizon_list[: len(sell_lr_r2)],
            sell_lr_r2,
            "o-",
            label="Linear Regression",
            color="blue",
        )
        ax3.plot(
            horizon_list[: len(sell_rf_r2)],
            sell_rf_r2,
            "s-",
            label="Random Forest",
            color="green",
        )
        ax3.set_xlabel("Prediction Horizon (seconds)")
        ax3.set_ylabel("Sell Volume R² Score")
        ax3.set_title("Sell Volume Prediction Performance")
        ax3.legend()
        ax3.grid(True, alpha=0.3)

    # 4. Layer distribution analysis
    ax4 = axes[1, 0]
    if "features_df" in result:
        features_df = result["features_df"]
        layer_counts = []
        for layer in range(min(10, result["num_layers"])):
            count = (features_df["Layer"] == layer).sum()
            layer_counts.append(count)

        ax4.bar(range(len(layer_counts)), layer_counts, color="skyblue", alpha=0.7)
        ax4.set_xlabel("Price Layer")
        ax4.set_ylabel("Number of Trades")
        ax4.set_title("Trade Distribution Across Layers (Top 10)")
        ax4.grid(True, alpha=0.3)

    # 5. Model comparison heatmap
    ax5 = axes[1, 1]
    if horizon_list and lr_accs:
        comparison_data = np.array([lr_accs, rf_accs])
        im = ax5.imshow(comparison_data, cmap="RdYlGn", aspect="auto", vmin=0, vmax=1)
        ax5.set_xticks(range(len(horizon_list)))
        ax5.set_xticklabels([f"{h}s" for h in horizon_list])
        ax5.set_yticks([0, 1])
        ax5.set_yticklabels(["Logistic Reg", "Random Forest"])
        ax5.set_title("Side Prediction Accuracy Heatmap")

        # Add text annotations
        for i in range(2):
            for j in range(len(horizon_list)):
                ax5.text(
                    j,
                    i,
                    f"{comparison_data[i, j]:.2f}",
                    ha="center",
                    va="center",
                    color="black",
                    fontsize=8,
                )

        plt.colorbar(im, ax=ax5)

    # 6. Summary statistics
    ax6 = axes[1, 2]
    summary_text = f"""
    LAYERED PREDICTION SUMMARY

    Trading Pair: {result['pair']}
    Number of Layers: {result['num_layers']}
    Data Size: {result['data_size']:,} trades
    Feature Size: {result['feature_size']:,} samples

    Prediction Horizons: {len(horizons)}
    ({min(horizons)}s to {max(horizons)}s)

    Best Side Accuracy:
    LR: {max(lr_accs) if lr_accs else 0:.1%}
    RF: {max(rf_accs) if rf_accs else 0:.1%}

    Best Volume R²:
    Buy: {max(buy_rf_r2) if buy_rf_r2 else 0:.3f}
    Sell: {max(sell_rf_r2) if sell_rf_r2 else 0:.3f}
    """

    ax6.text(
        0.05,
        0.95,
        summary_text,
        transform=ax6.transAxes,
        fontsize=10,
        verticalalignment="top",
        bbox=dict(boxstyle="round", facecolor="lightblue"),
    )
    ax6.set_title("Analysis Summary")
    ax6.axis("off")

    plt.tight_layout()
    plt.savefig(
        f'modeling/layered_prediction_{result["pair"]}.png',
        dpi=300,
        bbox_inches="tight",
    )
    plt.show()


def create_comprehensive_visualizations(
    results: List[Dict], save_dir: str = "modeling"
):
    """
    Create comprehensive visualizations for all coin prediction results.

    Args:
        results: List of analysis results for different coins
        save_dir: Directory to save visualization files
    """
    print("\n🎨 CREATING COMPREHENSIVE VISUALIZATIONS")
    print("=" * 60)

    # Separate results
    obi_results = [r for r in results if r.get("status") == "partial"]
    traditional_results = [r for r in results if "models" in r and "pair" in r]

    # Set style
    plt.style.use("default")
    sns.set_palette("husl")

    # 1. OBI Features Visualization
    if obi_results:
        create_obi_features_visualization(obi_results, save_dir)

    # 2. Model Performance Comparison
    if traditional_results:
        create_model_performance_visualization(traditional_results, save_dir)

    # 3. Cross-Coin Performance Analysis
    if traditional_results:
        create_cross_coin_analysis(traditional_results, save_dir)

    # 4. Feature Importance Analysis
    if traditional_results:
        create_feature_importance_visualization(traditional_results, save_dir)

    print(f"✅ All visualizations saved to {save_dir}/ directory")


def create_obi_features_visualization(obi_results: List[Dict], save_dir: str):
    """Create visualizations for OBI features"""
    print("📊 Creating OBI features visualization...")

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle("OBI Features Analysis Across Coins", fontsize=16, fontweight="bold")

    # Collect OBI data from all coins
    all_obi_data = []
    coin_names = []

    for i, result in enumerate(obi_results):
        if "obi_features" in result:
            obi_df = result["obi_features"]
            obi_cols = [col for col in obi_df.columns if col.startswith("obi_")]

            if len(obi_cols) > 0 and len(obi_df) > 0:
                # Sample OBI features for visualization
                sample_obi = obi_df[obi_cols[:10]].dropna()  # First 10 OBI features
                if len(sample_obi) > 0:
                    all_obi_data.append(sample_obi)
                    coin_names.append(f"Coin_{i+1}")

    if all_obi_data:
        # 1. OBI Distribution Heatmap
        ax1 = axes[0, 0]
        combined_obi = pd.concat(all_obi_data, keys=coin_names)
        obi_means = combined_obi.groupby(level=0).mean()

        sns.heatmap(obi_means.T, annot=True, fmt=".3f", cmap="RdBu_r", center=0, ax=ax1)
        ax1.set_title("OBI Features Mean Values by Coin")
        ax1.set_xlabel("Coins")
        ax1.set_ylabel("OBI Features")

        # 2. OBI Feature Distribution
        ax2 = axes[0, 1]
        sample_features = combined_obi.iloc[:, :5]  # First 5 features
        sample_features.boxplot(ax=ax2)
        ax2.set_title("OBI Feature Value Distributions")
        ax2.set_xlabel("OBI Features")
        ax2.set_ylabel("Values")
        ax2.tick_params(axis="x", rotation=45)

        # 3. OBI Time Series (if available)
        ax3 = axes[1, 0]
        if len(all_obi_data) > 0:
            sample_df = all_obi_data[0]
            if len(sample_df) > 100:
                # Plot first few OBI features over time
                for i, col in enumerate(sample_df.columns[:3]):
                    ax3.plot(sample_df[col].iloc[:100], label=col, alpha=0.7)
                ax3.set_title("OBI Features Time Series (Sample)")
                ax3.set_xlabel("Time Steps")
                ax3.set_ylabel("OBI Values")
                ax3.legend()
                ax3.grid(True, alpha=0.3)

        # 4. OBI Statistics Summary
        ax4 = axes[1, 1]
        obi_stats = []
        for coin_data in all_obi_data:
            stats = {
                "Mean": coin_data.mean().mean(),
                "Std": coin_data.std().mean(),
                "Min": coin_data.min().min(),
                "Max": coin_data.max().max(),
            }
            obi_stats.append(stats)

        if obi_stats:
            stats_df = pd.DataFrame(obi_stats, index=coin_names)
            stats_df.plot(kind="bar", ax=ax4)
            ax4.set_title("OBI Statistics Summary by Coin")
            ax4.set_xlabel("Coins")
            ax4.set_ylabel("Values")
            ax4.legend()
            ax4.tick_params(axis="x", rotation=45)

    plt.tight_layout()
    plt.savefig(f"{save_dir}/obi_features_analysis.png", dpi=300, bbox_inches="tight")
    plt.close()


def create_model_performance_visualization(
    traditional_results: List[Dict], save_dir: str
):
    """Create model performance visualizations"""
    print("🎯 Creating model performance visualization...")

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(
        "Model Performance Analysis Across Coins", fontsize=16, fontweight="bold"
    )

    # Collect performance data
    performance_data = []

    for result in traditional_results:
        pair = result.get("pair", "Unknown")
        if "models" in result:
            models = result["models"]
            horizons = result.get("horizons", [])

            for horizon in sorted(horizons):
                if horizon in models["horizons"]:
                    horizon_models = models["horizons"][horizon]

                    row = {
                        "Pair": pair,
                        "Horizon": horizon,
                        "Data_Size": result.get("data_size", 0),
                        "Feature_Size": result.get("feature_size", 0),
                    }

                    # Side prediction accuracy
                    if "side_test_acc" in horizon_models:
                        row["Side_LR"] = horizon_models["side_test_acc"].get(
                            "logistic", 0
                        )
                        row["Side_RF"] = horizon_models["side_test_acc"].get(
                            "random_forest", 0
                        )

                    # Volume prediction R²
                    if "buy_volume_test_r2" in horizon_models:
                        row["Buy_LASSO"] = horizon_models["buy_volume_test_r2"].get(
                            "lasso", 0
                        )
                        row["Buy_RF"] = horizon_models["buy_volume_test_r2"].get(
                            "random_forest", 0
                        )

                    if "sell_volume_test_r2" in horizon_models:
                        row["Sell_LASSO"] = horizon_models["sell_volume_test_r2"].get(
                            "lasso", 0
                        )
                        row["Sell_RF"] = horizon_models["sell_volume_test_r2"].get(
                            "random_forest", 0
                        )

                    performance_data.append(row)

    if performance_data:
        perf_df = pd.DataFrame(performance_data)

        # 1. Side Prediction Accuracy by Horizon
        ax1 = axes[0, 0]
        if "Side_RF" in perf_df.columns:
            side_perf = perf_df.groupby("Horizon")["Side_RF"].mean()
            side_perf.plot(kind="line", marker="o", ax=ax1, color="blue")
            ax1.set_title("Side Prediction Accuracy vs Horizon")
            ax1.set_xlabel("Prediction Horizon (seconds)")
            ax1.set_ylabel("Random Forest Accuracy")
            ax1.grid(True, alpha=0.3)

        # 2. Volume Prediction R² by Horizon
        ax2 = axes[0, 1]
        if "Buy_LASSO" in perf_df.columns:
            vol_perf = perf_df.groupby("Horizon")[["Buy_LASSO", "Sell_LASSO"]].mean()
            vol_perf.plot(kind="line", marker="o", ax=ax2)
            ax2.set_title("Volume Prediction R² vs Horizon (LASSO)")
            ax2.set_xlabel("Prediction Horizon (seconds)")
            ax2.set_ylabel("R² Score")
            ax2.legend(["Buy Volume", "Sell Volume"])
            ax2.grid(True, alpha=0.3)

        # 3. Performance Heatmap by Coin
        ax3 = axes[0, 2]
        if "Side_RF" in perf_df.columns and len(perf_df["Pair"].unique()) > 1:
            pivot_data = perf_df.pivot_table(
                values="Side_RF", index="Pair", columns="Horizon", aggfunc="mean"
            )
            sns.heatmap(pivot_data, annot=True, fmt=".2f", cmap="YlOrRd", ax=ax3)
            ax3.set_title("Side Prediction Accuracy Heatmap")
            ax3.set_xlabel("Horizon (seconds)")
            ax3.set_ylabel("Trading Pairs")

        # 4. Model Comparison
        ax4 = axes[1, 0]
        if "Side_LR" in perf_df.columns and "Side_RF" in perf_df.columns:
            model_comp = perf_df[["Side_LR", "Side_RF"]].mean()
            model_comp.plot(kind="bar", ax=ax4, color=["lightblue", "darkblue"])
            ax4.set_title("Model Comparison: Side Prediction")
            ax4.set_xlabel("Models")
            ax4.set_ylabel("Average Accuracy")
            ax4.tick_params(axis="x", rotation=45)

        # 5. LASSO vs Random Forest for Volume
        ax5 = axes[1, 1]
        if "Buy_LASSO" in perf_df.columns and "Buy_RF" in perf_df.columns:
            lasso_vs_rf = perf_df[
                ["Buy_LASSO", "Buy_RF", "Sell_LASSO", "Sell_RF"]
            ].mean()
            lasso_vs_rf.plot(
                kind="bar", ax=ax5, color=["red", "blue", "orange", "green"]
            )
            ax5.set_title("LASSO vs Random Forest: Volume Prediction")
            ax5.set_xlabel("Model-Volume Type")
            ax5.set_ylabel("Average R² Score")
            ax5.tick_params(axis="x", rotation=45)

        # 6. Data Size vs Performance
        ax6 = axes[1, 2]
        if "Data_Size" in perf_df.columns and "Side_RF" in perf_df.columns:
            ax6.scatter(
                perf_df["Data_Size"], perf_df["Side_RF"], alpha=0.6, color="purple"
            )
            ax6.set_title("Data Size vs Performance")
            ax6.set_xlabel("Data Size (number of trades)")
            ax6.set_ylabel("Side Prediction Accuracy")
            ax6.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(
        f"{save_dir}/model_performance_analysis.png", dpi=300, bbox_inches="tight"
    )
    plt.close()


def create_cross_coin_analysis(traditional_results: List[Dict], save_dir: str):
    """Create cross-coin performance analysis"""
    print("🔄 Creating cross-coin analysis...")

    fig, axes = plt.subplots(2, 2, figsize=(16, 10))
    fig.suptitle("Cross-Coin Performance Analysis", fontsize=16, fontweight="bold")

    # Collect summary data
    coin_summary = []

    for result in traditional_results:
        pair = result.get("pair", "Unknown")
        if "models" in result:
            models = result["models"]
            horizons = result.get("horizons", [])

            # Calculate average performance across horizons
            side_accs = []
            vol_r2s = []

            for horizon in horizons:
                if horizon in models["horizons"]:
                    horizon_models = models["horizons"][horizon]

                    if "side_test_acc" in horizon_models:
                        side_accs.append(
                            horizon_models["side_test_acc"].get("random_forest", 0)
                        )

                    if "buy_volume_test_r2" in horizon_models:
                        vol_r2s.append(
                            horizon_models["buy_volume_test_r2"].get("lasso", 0)
                        )

            if side_accs or vol_r2s:
                coin_summary.append(
                    {
                        "Pair": pair,
                        "Avg_Side_Acc": np.mean(side_accs) if side_accs else 0,
                        "Avg_Vol_R2": np.mean(vol_r2s) if vol_r2s else 0,
                        "Data_Size": result.get("data_size", 0),
                        "Feature_Size": result.get("feature_size", 0),
                        "Num_Horizons": len(horizons),
                    }
                )

    if coin_summary:
        summary_df = pd.DataFrame(coin_summary)

        # 1. Coin Performance Ranking
        ax1 = axes[0, 0]
        summary_df_sorted = summary_df.sort_values("Avg_Side_Acc", ascending=True)
        summary_df_sorted.plot(
            x="Pair", y="Avg_Side_Acc", kind="barh", ax=ax1, color="skyblue"
        )
        ax1.set_title("Average Side Prediction Accuracy by Coin")
        ax1.set_xlabel("Average Accuracy")
        ax1.set_ylabel("Trading Pairs")

        # 2. Volume Prediction Performance
        ax2 = axes[0, 1]
        summary_df_sorted_vol = summary_df.sort_values("Avg_Vol_R2", ascending=True)
        summary_df_sorted_vol.plot(
            x="Pair", y="Avg_Vol_R2", kind="barh", ax=ax2, color="lightcoral"
        )
        ax2.set_title("Average Volume Prediction R² by Coin")
        ax2.set_xlabel("Average R² Score")
        ax2.set_ylabel("Trading Pairs")

        # 3. Performance vs Data Size
        ax3 = axes[1, 0]
        ax3.scatter(
            summary_df["Data_Size"],
            summary_df["Avg_Side_Acc"],
            s=100,
            alpha=0.7,
            color="green",
        )
        for i, row in summary_df.iterrows():
            ax3.annotate(
                row["Pair"],
                (row["Data_Size"], row["Avg_Side_Acc"]),
                xytext=(5, 5),
                textcoords="offset points",
                fontsize=8,
            )
        ax3.set_title("Performance vs Data Size")
        ax3.set_xlabel("Data Size (number of trades)")
        ax3.set_ylabel("Average Side Prediction Accuracy")
        ax3.grid(True, alpha=0.3)

        # 4. Summary Statistics
        ax4 = axes[1, 1]
        stats_data = {
            "Side Accuracy": [
                summary_df["Avg_Side_Acc"].mean(),
                summary_df["Avg_Side_Acc"].std(),
            ],
            "Volume R²": [
                summary_df["Avg_Vol_R2"].mean(),
                summary_df["Avg_Vol_R2"].std(),
            ],
        }

        x_pos = np.arange(len(stats_data))
        means = [stats_data[key][0] for key in stats_data.keys()]
        stds = [stats_data[key][1] for key in stats_data.keys()]

        ax4.bar(x_pos, means, yerr=stds, capsize=5, color=["blue", "red"], alpha=0.7)
        ax4.set_title("Overall Performance Statistics")
        ax4.set_xlabel("Metrics")
        ax4.set_ylabel("Values")
        ax4.set_xticks(x_pos)
        ax4.set_xticklabels(stats_data.keys())
        ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(f"{save_dir}/cross_coin_analysis.png", dpi=300, bbox_inches="tight")
    plt.close()


def create_feature_importance_visualization(
    traditional_results: List[Dict], save_dir: str
):
    """Create feature importance analysis from LASSO models"""
    print("🔍 Creating feature importance visualization...")

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(
        "Feature Importance Analysis (LASSO Models)", fontsize=16, fontweight="bold"
    )

    # Collect feature importance data
    all_feature_importance = []
    feature_names = []

    for result in traditional_results:
        pair = result.get("pair", "Unknown")
        if "models" in result:
            models = result["models"]

            # Get feature names
            if "feature_cols" in models:
                feature_names = models["feature_cols"]

            # Collect LASSO coefficients
            for horizon in models.get("horizons", {}):
                horizon_models = models["horizons"][horizon]

                # Buy volume LASSO model
                if "buy_volume_models" in horizon_models:
                    if "lasso" in horizon_models["buy_volume_models"]:
                        lasso_model = horizon_models["buy_volume_models"]["lasso"][
                            "model"
                        ]
                        if hasattr(lasso_model, "coef_"):
                            all_feature_importance.append(
                                {
                                    "pair": pair,
                                    "horizon": horizon,
                                    "type": "buy_volume",
                                    "coefficients": lasso_model.coef_,
                                }
                            )

    if all_feature_importance and feature_names:
        # 1. Average Feature Importance
        ax1 = axes[0, 0]
        all_coefs = [item["coefficients"] for item in all_feature_importance]
        if all_coefs:
            avg_coefs = np.mean(np.abs(all_coefs), axis=0)

            # Get top 15 features
            top_indices = np.argsort(avg_coefs)[-15:]
            top_features = [feature_names[i] for i in top_indices]
            top_values = avg_coefs[top_indices]

            ax1.barh(range(len(top_features)), top_values, color="steelblue")
            ax1.set_yticks(range(len(top_features)))
            ax1.set_yticklabels(top_features, fontsize=8)
            ax1.set_title("Top 15 Most Important Features (Average)")
            ax1.set_xlabel("Average |Coefficient|")

        # 2. Feature Type Distribution
        ax2 = axes[0, 1]
        feature_types = {}
        for fname in feature_names:
            if fname.startswith("obi_"):
                ftype = "OBI Features"
            elif fname.startswith("order_flow"):
                ftype = "Order Flow"
            elif fname.startswith("volume_"):
                ftype = "Volume Features"
            elif fname.startswith("buy_ratio"):
                ftype = "Buy Ratio"
            else:
                ftype = "Other"

            feature_types[ftype] = feature_types.get(ftype, 0) + 1

        if feature_types:
            ax2.pie(
                feature_types.values(), labels=feature_types.keys(), autopct="%1.1f%%"
            )
            ax2.set_title("Feature Type Distribution")

        # 3. Coefficient Distribution
        ax3 = axes[1, 0]
        all_coef_values = np.concatenate(all_coefs) if all_coefs else []
        if len(all_coef_values) > 0:
            ax3.hist(all_coef_values, bins=50, alpha=0.7, color="green")
            ax3.set_title("LASSO Coefficient Distribution")
            ax3.set_xlabel("Coefficient Values")
            ax3.set_ylabel("Frequency")
            ax3.axvline(x=0, color="red", linestyle="--", alpha=0.7)

        # 4. Feature Selection Rate
        ax4 = axes[1, 1]
        if all_coefs:
            # Calculate how often each feature is selected (non-zero coefficient)
            selection_rates = []
            for i in range(len(feature_names)):
                non_zero_count = sum(1 for coefs in all_coefs if abs(coefs[i]) > 1e-6)
                selection_rates.append(non_zero_count / len(all_coefs))

            # Show top 15 most selected features
            top_selected_indices = np.argsort(selection_rates)[-15:]
            top_selected_features = [feature_names[i] for i in top_selected_indices]
            top_selected_rates = [selection_rates[i] for i in top_selected_indices]

            ax4.barh(
                range(len(top_selected_features)), top_selected_rates, color="orange"
            )
            ax4.set_yticks(range(len(top_selected_features)))
            ax4.set_yticklabels(top_selected_features, fontsize=8)
            ax4.set_title("Top 15 Most Selected Features")
            ax4.set_xlabel("Selection Rate")

    plt.tight_layout()
    plt.savefig(
        f"{save_dir}/feature_importance_analysis.png", dpi=300, bbox_inches="tight"
    )
    plt.close()


def visualize_layered_results(result: Dict):
    """Create comprehensive visualizations for layered prediction results"""

    if not result or "models" not in result:
        print("No valid results to visualize")
        return

    models = result["models"]
    pair = result["pair"]
    horizons = result["horizons"]

    # Create subplots
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f"Layered Prediction Results - {pair}", fontsize=16)

    # 1. Side prediction accuracy across horizons
    ax1 = axes[0, 0]
    side_accuracies = {"logistic": [], "random_forest": []}
    horizon_list = []

    for horizon in sorted(horizons):
        if horizon in models["horizons"]:
            horizon_models = models["horizons"][horizon]
            if "side_test_acc" in horizon_models:
                horizon_list.append(horizon)
                side_accuracies["logistic"].append(
                    horizon_models["side_test_acc"].get("logistic", 0)
                )
                side_accuracies["random_forest"].append(
                    horizon_models["side_test_acc"].get("random_forest", 0)
                )

    if horizon_list:
        ax1.plot(horizon_list, side_accuracies["logistic"], "o-", label="Logistic")
        ax1.plot(
            horizon_list, side_accuracies["random_forest"], "s-", label="Random Forest"
        )
        ax1.set_xlabel("Prediction Horizon (seconds)")
        ax1.set_ylabel("Accuracy")
        ax1.set_title("Side Prediction Accuracy")
        ax1.legend()
        ax1.grid(True, alpha=0.3)

    # 2. Volume prediction R² for buy orders (updated for LASSO)
    ax2 = axes[0, 1]
    buy_r2 = {"lasso": [], "random_forest": []}

    for horizon in sorted(horizons):
        if horizon in models["horizons"]:
            horizon_models = models["horizons"][horizon]
            if "buy_volume_test_r2" in horizon_models:
                buy_r2["lasso"].append(
                    horizon_models["buy_volume_test_r2"].get("lasso", 0)
                )
                buy_r2["random_forest"].append(
                    horizon_models["buy_volume_test_r2"].get("random_forest", 0)
                )

    if horizon_list and buy_r2["lasso"]:
        ax2.plot(horizon_list, buy_r2["lasso"], "o-", label="LASSO")
        ax2.plot(horizon_list, buy_r2["random_forest"], "s-", label="Random Forest")
        ax2.set_xlabel("Prediction Horizon (seconds)")
        ax2.set_ylabel("R² Score")
        ax2.set_title("Buy Volume Prediction R²")
        ax2.legend()
        ax2.grid(True, alpha=0.3)

    # 3. Volume prediction R² for sell orders (updated for LASSO)
    ax3 = axes[0, 2]
    sell_r2 = {"lasso": [], "random_forest": []}

    for horizon in sorted(horizons):
        if horizon in models["horizons"]:
            horizon_models = models["horizons"][horizon]
            if "sell_volume_test_r2" in horizon_models:
                sell_r2["lasso"].append(
                    horizon_models["sell_volume_test_r2"].get("lasso", 0)
                )
                sell_r2["random_forest"].append(
                    horizon_models["sell_volume_test_r2"].get("random_forest", 0)
                )

    if horizon_list and sell_r2["lasso"]:
        ax3.plot(horizon_list, sell_r2["lasso"], "o-", label="LASSO")
        ax3.plot(horizon_list, sell_r2["random_forest"], "s-", label="Random Forest")
        ax3.set_xlabel("Prediction Horizon (seconds)")
        ax3.set_ylabel("R² Score")
        ax3.set_title("Sell Volume Prediction R²")
        ax3.legend()
        ax3.grid(True, alpha=0.3)

    # 4. Feature importance (updated for OBI and order flow features)
    ax4 = axes[1, 0]
    feature_cols = models.get("feature_cols", [])
    if feature_cols:
        # Show feature categories
        feature_categories = {}
        for col in feature_cols:
            if col.startswith("obi_"):
                category = "OBI Features"
            elif col.startswith("order_flow"):
                category = "Order Flow"
            elif col.startswith("volume_"):
                category = "Volume"
            elif col.startswith("count_"):
                category = "Count"
            elif col.startswith("buy_ratio_"):
                category = "Buy Ratio"
            elif col.startswith("total_"):
                category = "Total"
            else:
                category = "Other"

            feature_categories[category] = feature_categories.get(category, 0) + 1

        if feature_categories:
            ax4.pie(
                feature_categories.values(),
                labels=feature_categories.keys(),
                autopct="%1.1f%%",
            )
            ax4.set_title("Feature Categories")

    # 5. Data statistics
    ax5 = axes[1, 1]
    stats_data = {
        "Data Size": result.get("data_size", 0),
        "Feature Size": result.get("feature_size", 0),
        "Layers": result.get("num_layers", 0),
        "Horizons": len(horizons),
    }

    bars = ax5.bar(range(len(stats_data)), list(stats_data.values()))
    ax5.set_xticks(range(len(stats_data)))
    ax5.set_xticklabels(list(stats_data.keys()), rotation=45)
    ax5.set_title("Dataset Statistics")
    ax5.set_ylabel("Count")

    # Add value labels on bars
    for i, (bar, value) in enumerate(zip(bars, stats_data.values())):
        ax5.text(
            bar.get_x() + bar.get_width() / 2,
            bar.get_height() + max(stats_data.values()) * 0.01,
            str(value),
            ha="center",
            va="bottom",
        )

    plt.tight_layout()
    plt.savefig(
        f'modeling/layered_prediction_{result["pair"]}.png',
        dpi=300,
        bbox_inches="tight",
    )
    plt.close()  # Close to save memory


def main():
    """Main function to run OBI-based layered prediction analysis"""

    print("🏗️ OBI LAYERED TRADING PREDICTION ANALYSIS")
    print("=" * 80)
    print("Features:")
    print("- OBI features from 40 layered orderbook data (±1% around midprice)")
    print("- Order flow features from past 1-minute buy-sell imbalance")
    print("- LASSO regression with L1 penalty")
    print("- Bayesian optimization for hyperparameter tuning")
    print("=" * 80)

    # Get available trading pairs
    available_pairs = get_available_trading_pairs()
    print(f"Found {len(available_pairs)} trading pairs: {available_pairs}")

    # Analyze first few pairs as examples
    pairs_to_analyze = available_pairs[:3]  # Analyze first 3 pairs for demo

    all_results = []

    for pair in pairs_to_analyze:
        print(f"\n🔄 Analyzing {pair} with OBI features...")

        # Use the new OBI-based analysis
        result = analyze_obi_layered_prediction(
            pair=pair,
            sample_size=5000,  # Smaller sample for faster processing
            num_layers=40,
            lookback_window=200,
            horizons=[10, 30, 60, 120, 200],  # Selected horizons
            max_orderbook_files=3,  # Limit files for demo
            max_trade_files=3,
            use_bayesian_opt=True,
        )

        if result and result.get("status") == "partial":
            print(f"✅ {pair}: OBI feature extraction complete")
            all_results.append(result)

            # Display sample features
            if "obi_features" in result:
                obi_df = result["obi_features"]
                print(f"OBI features shape: {obi_df.shape}")
                print("Sample OBI features:")
                obi_cols = [col for col in obi_df.columns if col.startswith("obi_")]
                print(obi_df[obi_cols[:5]].head())

            if "order_flow" in result:
                of_df = result["order_flow"]
                print(f"Order flow features shape: {of_df.shape}")
                print("Sample order flow features:")
                of_cols = ["order_flow", "net_volume", "volume_ratio", "buy_ratio"]
                available_of_cols = [col for col in of_cols if col in of_df.columns]
                print(of_df[available_of_cols].head())
        else:
            print(f"❌ {pair}: Analysis failed")

    # Fallback to traditional analysis for comparison
    print(f"\n🔄 Running traditional layered analysis for comparison...")
    traditional_result = analyze_layered_prediction(
        pair=pairs_to_analyze[0] if pairs_to_analyze else "BTC_USD",
        sample_size=3000,
        num_layers=40,
        lookback_window=200,
        horizons=[30, 60, 120],
    )

    if traditional_result:
        print(f"✅ Traditional analysis complete")
        all_results.append(traditional_result)

    if all_results:
        print(f"\n📊 OBI LAYERED PREDICTION ANALYSIS SUMMARY")
        print("=" * 80)
        print(f"Successfully processed {len(all_results)} analysis runs")

        # Count different types of results
        obi_results = [r for r in all_results if r.get("status") == "partial"]
        traditional_results = [r for r in all_results if "models" in r]

        print(f"OBI feature extractions: {len(obi_results)}")
        print(f"Traditional model training: {len(traditional_results)}")

        # Summary of OBI features
        if obi_results:
            print(f"\n🎯 OBI FEATURES SUMMARY:")
            for result in obi_results:
                if "obi_features" in result:
                    obi_df = result["obi_features"]
                    obi_cols = [col for col in obi_df.columns if col.startswith("obi_")]
                    print(
                        f"  - Generated {len(obi_cols)} OBI features from {len(obi_df)} timestamps"
                    )

                if "order_flow" in result:
                    of_df = result["order_flow"]
                    of_cols = [
                        col
                        for col in of_df.columns
                        if col.startswith(("order_flow", "net_volume", "volume_ratio"))
                    ]
                    print(
                        f"  - Generated {len(of_cols)} order flow features from {len(of_df)} timestamps"
                    )

        # Summary of traditional results
        if traditional_results:
            print(f"\n🎯 MODEL PERFORMANCE SUMMARY:")
            summary_data = []
            for result in traditional_results:
                if "models" in result and "horizons" in result:
                    models = result["models"]
                    for horizon in result["horizons"]:
                        if horizon in models["horizons"]:
                            horizon_models = models["horizons"][horizon]

                            summary_row = {
                                "pair": result["pair"],
                                "horizon": horizon,
                                "num_layers": result["num_layers"],
                                "data_size": result["data_size"],
                            }

                            # Add model performance metrics (updated for LASSO)
                            if "side_test_acc" in horizon_models:
                                summary_row["side_acc_lr"] = horizon_models[
                                    "side_test_acc"
                                ].get("logistic", 0)
                                summary_row["side_acc_rf"] = horizon_models[
                                    "side_test_acc"
                                ].get("random_forest", 0)

                            if "buy_volume_test_r2" in horizon_models:
                                summary_row["buy_vol_r2_lasso"] = horizon_models[
                                    "buy_volume_test_r2"
                                ].get("lasso", 0)
                                summary_row["buy_vol_r2_rf"] = horizon_models[
                                    "buy_volume_test_r2"
                                ].get("random_forest", 0)

                            if "sell_volume_test_r2" in horizon_models:
                                summary_row["sell_vol_r2_lasso"] = horizon_models[
                                    "sell_volume_test_r2"
                                ].get("lasso", 0)
                                summary_row["sell_vol_r2_rf"] = horizon_models[
                                    "sell_volume_test_r2"
                                ].get("random_forest", 0)

                            summary_data.append(summary_row)

            if summary_data:
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_csv("obi_layered_prediction_results.csv", index=False)
                print(f"Results saved to 'obi_layered_prediction_results.csv'")

                # Print best performers
                if "side_acc_rf" in summary_df.columns:
                    best_side = summary_df.loc[summary_df["side_acc_rf"].idxmax()]
                    print(
                        f"🏆 Best Side Prediction: {best_side['pair']} at {best_side['horizon']}s - {best_side['side_acc_rf']:.1%}"
                    )

                if "buy_vol_r2_lasso" in summary_df.columns:
                    best_buy_vol = summary_df.loc[
                        summary_df["buy_vol_r2_lasso"].idxmax()
                    ]
                    print(
                        f"🏆 Best Buy Volume (LASSO): {best_buy_vol['pair']} at {best_buy_vol['horizon']}s - R²={best_buy_vol['buy_vol_r2_lasso']:.3f}"
                    )

        # Print detailed results for each coin
        print(f"\n📋 PRINTING DETAILED RESULTS...")
        print_coin_prediction_results(all_results, save_to_file=True)

        # Create comprehensive visualizations
        print(f"\n🎨 CREATING VISUALIZATIONS...")
        create_comprehensive_visualizations(all_results, save_dir="modeling")

        # Create individual visualizations for traditional results
        traditional_results = [r for r in all_results if "models" in r and "pair" in r]
        if traditional_results:
            print(f"\n📊 Creating individual coin visualizations...")
            for result in traditional_results:
                print(f"  - Creating visualization for {result.get('pair', 'Unknown')}")
                visualize_layered_results(result)

        print(f"\n🎉 ANALYSIS COMPLETE!")
        print("Key improvements implemented:")
        print("✅ OBI features from 40 layered orderbook data (±1% around midprice)")
        print("✅ Order flow features from past 1-minute buy-sell imbalance")
        print("✅ LASSO regression with L1 penalty for feature selection")
        print("✅ Bayesian optimization for hyperparameter tuning")
        print("✅ Support for both orderbook and trade data analysis")
        print("✅ Comprehensive result printing and visualization")

        print(f"\n📁 OUTPUT FILES GENERATED:")
        print("  📊 Terminal Results:")
        print("    - coin_prediction_results_[timestamp].txt")
        print("    - obi_layered_prediction_results.csv")
        print("  🎨 Visualizations:")
        print("    - obi_features_analysis.png")
        print("    - model_performance_analysis.png")
        print("    - cross_coin_analysis.png")
        print("    - feature_importance_analysis.png")
        print("    - layered_prediction_[coin].png (individual coins)")

        return all_results

    return None


if __name__ == "__main__":
    results = main()

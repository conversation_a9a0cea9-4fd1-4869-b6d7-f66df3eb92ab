#!/usr/bin/env python3
"""
Test script for the improved OBI layered prediction analysis.

This script demonstrates the new features:
1. OBI features from 40 layered orderbook data (±1% around midprice)
2. Order flow features from past 1-minute buy-sell imbalance
3. LASSO regression with L1 penalty
4. Bayesian optimization for hyperparameter tuning

Usage:
    python test_obi_features.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from layered_prediction_analysis import (
    analyze_obi_layered_prediction,
    get_available_trading_pairs,
    install_dependencies,
    BAYESIAN_OPT_AVAILABLE
)

def test_obi_features():
    """Test the new OBI feature extraction functionality"""
    
    print("🧪 TESTING OBI LAYERED PREDICTION FEATURES")
    print("=" * 60)
    
    # Check if Bayesian optimization is available
    if not BAYESIAN_OPT_AVAILABLE:
        print("⚠️  Bayesian optimization not available.")
        print("Installing dependencies...")
        install_dependencies()
    
    # Get available trading pairs
    try:
        pairs = get_available_trading_pairs()
        if not pairs:
            print("❌ No trading pairs found. Please check data directory.")
            return
        
        print(f"✅ Found {len(pairs)} trading pairs: {pairs[:5]}...")
        
        # Test with the first available pair
        test_pair = pairs[0]
        print(f"\n🔬 Testing OBI features with {test_pair}")
        
        # Run OBI analysis with small sample for testing
        result = analyze_obi_layered_prediction(
            pair=test_pair,
            sample_size=1000,  # Small sample for quick testing
            num_layers=40,
            lookback_window=100,
            horizons=[30, 60],  # Just 2 horizons for testing
            max_orderbook_files=2,  # Limit files for quick testing
            max_trade_files=2,
            use_bayesian_opt=BAYESIAN_OPT_AVAILABLE,
        )
        
        if result and result.get("status") == "partial":
            print("✅ OBI feature extraction successful!")
            
            # Display OBI features
            if "obi_features" in result:
                obi_df = result["obi_features"]
                print(f"\n📊 OBI Features Summary:")
                print(f"  - Shape: {obi_df.shape}")
                
                obi_cols = [col for col in obi_df.columns if col.startswith("obi_")]
                print(f"  - OBI feature columns: {len(obi_cols)}")
                print(f"  - Sample columns: {obi_cols[:5]}")
                
                if len(obi_df) > 0:
                    print(f"  - Sample values:")
                    for col in obi_cols[:3]:
                        values = obi_df[col].dropna()
                        if len(values) > 0:
                            print(f"    {col}: mean={values.mean():.4f}, std={values.std():.4f}")
            
            # Display order flow features
            if "order_flow" in result:
                of_df = result["order_flow"]
                print(f"\n📈 Order Flow Features Summary:")
                print(f"  - Shape: {of_df.shape}")
                
                of_cols = ["order_flow", "net_volume", "volume_ratio", "buy_ratio"]
                available_cols = [col for col in of_cols if col in of_df.columns]
                print(f"  - Available columns: {available_cols}")
                
                if len(of_df) > 0:
                    print(f"  - Sample values:")
                    for col in available_cols:
                        values = of_df[col].dropna()
                        if len(values) > 0:
                            print(f"    {col}: mean={values.mean():.4f}, std={values.std():.4f}")
            
            print("\n✅ Test completed successfully!")
            print("\nNext steps:")
            print("1. Run full analysis with: python layered_prediction_analysis.py")
            print("2. Adjust parameters in the main() function as needed")
            print("3. Check results in 'obi_layered_prediction_results.csv'")
            
        else:
            print("❌ OBI feature extraction failed")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

def test_feature_descriptions():
    """Display detailed descriptions of the new features"""
    
    print("\n📚 FEATURE DESCRIPTIONS")
    print("=" * 60)
    
    print("\n🎯 OBI (Order Book Imbalance) Features:")
    print("  - Based on 40 price layers within ±1% of midprice")
    print("  - 20 layers above midprice, 20 layers below")
    print("  - Individual layer OBI: (volume_above - volume_below) / (volume_above + volume_below)")
    print("  - Cumulative layer OBI: cumulative volumes up to layer i")
    print("  - Total: ~40 OBI features per timestamp")
    
    print("\n📊 Order Flow (OF) Features:")
    print("  - Based on past 1-minute trade data")
    print("  - order_flow: buy_size - sell_size in past minute")
    print("  - net_volume: buy_volume - sell_volume")
    print("  - volume_ratio: buy_volume / sell_volume")
    print("  - buy_ratio: buy_count / total_count")
    print("  - Total: ~8 order flow features per timestamp")
    
    print("\n🔧 Model Improvements:")
    print("  - LASSO regression with L1 penalty for automatic feature selection")
    print("  - Bayesian optimization for hyperparameter tuning")
    print("  - Optimizes LASSO alpha and classifier parameters")
    print("  - Reduces overfitting and improves generalization")
    
    print("\n📈 Expected Benefits:")
    print("  - Better capture of orderbook microstructure")
    print("  - Improved prediction of trade direction and volume")
    print("  - Automatic feature selection reduces noise")
    print("  - Optimized hyperparameters improve performance")

if __name__ == "__main__":
    test_feature_descriptions()
    test_obi_features()

# Coin Prediction Analysis - Results and Visualizations

This guide explains how to run the improved coin prediction analysis and view the comprehensive results and visualizations.

## 🚀 Quick Start

### 1. Run Analysis
```bash
# Full analysis with all features
python modeling/run_coin_analysis.py --full

# Quick demo (faster, smaller dataset)
python modeling/run_coin_analysis.py --demo

# Default analysis
python modeling/layered_prediction_analysis.py
```

### 2. View Results
```bash
# Interactive results viewer
python modeling/view_results.py

# Test new features
python modeling/test_obi_features.py
```

## 📊 Generated Output Files

### Terminal Results
- **`coin_prediction_results_[timestamp].txt`**: Detailed terminal output with all coin results
- **`obi_layered_prediction_results.csv`**: Model performance metrics in CSV format

### Visualizations (PNG Files)
- **`obi_features_analysis.png`**: OBI features analysis across coins
- **`model_performance_analysis.png`**: Model performance comparison
- **`cross_coin_analysis.png`**: Cross-coin performance analysis  
- **`feature_importance_analysis.png`**: LASSO feature importance
- **`layered_prediction_[coin].png`**: Individual coin performance charts
- **`visualization_summary.png`**: Grid view of all visualizations

## 🎯 What You'll See in Terminal

### OBI Feature Extraction Results
```
📊 OBI FEATURE EXTRACTION RESULTS
────────────────────────────────────────────────────────────

Coin #1
  📈 OBI Features:
    - Total timestamps: 1,234
    - OBI feature columns: 40
    - Time range: 2024-01-01 to 2024-01-02
  📊 Sample OBI Statistics:
    obi_layer_1:
      Mean:   0.0123 | Std:   0.0456
      Range: [-0.1234,  0.1234]
```

### Model Performance Results
```
🤖 MODEL PREDICTION RESULTS
────────────────────────────────────────────────────────────

💰 BTC_USD
══════════════════════════════════════════════════════════
📊 Dataset Info:
  - Data size: 5,000 trades
  - Feature size: 4,500 samples
  - Layers: 40
  - Horizons: [30, 60, 120]

🎯 Prediction Performance:

  Performance Table:
  ────────────────────────────────────────────────────────────────────────────────
  Horizon  Side LR  Side RF  Buy LASSO  Buy RF   Sell LASSO  Sell RF
  ────────────────────────────────────────────────────────────────────────────────
  30s      65.2%    72.1%    0.234      0.189    0.198       0.156
  60s      63.8%    70.5%    0.267      0.201    0.223       0.178
  120s     61.4%    68.9%    0.289      0.234    0.245       0.198

🏆 Best Performance:
  - Best Side Prediction: 72.1% at 30s
  - Best Volume Prediction: R² = 0.289 at 120s
```

## 🎨 Visualization Details

### 1. OBI Features Analysis (`obi_features_analysis.png`)
- **Heatmap**: OBI feature mean values across coins
- **Box Plots**: Distribution of OBI feature values
- **Time Series**: Sample OBI features over time
- **Statistics**: Summary statistics by coin

### 2. Model Performance Analysis (`model_performance_analysis.png`)
- **Side Prediction**: Accuracy vs prediction horizon
- **Volume Prediction**: R² scores for LASSO and Random Forest
- **Performance Heatmap**: Results across coins and horizons
- **Model Comparison**: LASSO vs Random Forest performance
- **Data Size Impact**: Performance vs dataset size

### 3. Cross-Coin Analysis (`cross_coin_analysis.png`)
- **Coin Rankings**: Performance ranking across all coins
- **Volume Prediction**: R² scores by coin
- **Performance vs Data Size**: Scatter plot analysis
- **Summary Statistics**: Overall performance metrics

### 4. Feature Importance Analysis (`feature_importance_analysis.png`)
- **Top Features**: Most important features from LASSO models
- **Feature Types**: Distribution of OBI, order flow, and other features
- **Coefficient Distribution**: LASSO coefficient histogram
- **Selection Rate**: How often each feature is selected

### 5. Individual Coin Charts (`layered_prediction_[coin].png`)
- **Side Prediction**: Accuracy across horizons
- **Volume Prediction**: Buy/sell volume R² scores (LASSO vs RF)
- **Feature Categories**: Pie chart of feature types
- **Dataset Statistics**: Data size, features, layers, horizons

## 📋 Key Features Implemented

### ✅ OBI Features (Order Book Imbalance)
- **40 Price Layers**: Within ±1% of midprice
- **Individual Layer OBI**: `(volume_above - volume_below) / (volume_above + volume_below)`
- **Cumulative Layer OBI**: Cumulative volumes across layers
- **Real-time Calculation**: From orderbook snapshots

### ✅ Order Flow Features
- **1-Minute Window**: `order_flow = past_1min_buy_size - past_1min_sell_size`
- **Volume Metrics**: Net volume, volume ratio, buy ratio
- **Trade Metrics**: Trade count, buy/sell counts

### ✅ LASSO Regression
- **L1 Penalty**: Automatic feature selection
- **Bayesian Optimization**: Optimal alpha parameter tuning
- **Feature Selection**: Identifies most predictive features

### ✅ Comprehensive Output
- **Terminal Results**: Detailed performance tables
- **File Output**: Text and CSV results
- **Visualizations**: 5+ different chart types
- **Individual Analysis**: Per-coin detailed charts

## 🔧 Configuration Options

### Analysis Parameters
```python
# In run_coin_analysis.py or layered_prediction_analysis.py
sample_size=3000,           # Number of trades to analyze
num_layers=40,              # Price layers (20 above + 20 below midprice)
lookback_window=200,        # Window for traditional features
horizons=[30, 60, 120],     # Prediction horizons in seconds
max_orderbook_files=2,      # Limit orderbook files for speed
max_trade_files=2,          # Limit trade files for speed
use_bayesian_opt=True,      # Enable Bayesian optimization
```

### Visualization Settings
```python
# Modify in create_comprehensive_visualizations()
save_dir="modeling"         # Output directory
dpi=300                     # Image resolution
figsize=(16, 12)           # Figure size
```

## 🎯 Trading Strategy Integration

### OBI Thresholds (Your Preferred Strategy)
- **Sell Signal**: `-1 < obi < -1/3` (orderbook favors selling)
- **Hold Signal**: `-1/3 < obi < 1/3` (balanced orderbook)
- **Buy Signal**: `1/3 < obi < 1` (orderbook favors buying)

### Multi-Layer Analysis
- **40 Layers**: Fine-grained price level analysis
- **Cumulative OBI**: Broader market sentiment
- **Order Flow**: Recent trading momentum

### Model Selection
- **LASSO**: Automatic feature selection, identifies key OBI levels
- **Random Forest**: Captures non-linear relationships
- **Bayesian Optimization**: Optimal hyperparameters

## 🚨 Troubleshooting

### Common Issues
1. **No data files found**: Check `Data_Fetching/Richard_data/` directory
2. **Memory errors**: Reduce `sample_size` parameter
3. **Slow performance**: Reduce `max_orderbook_files` and `max_trade_files`
4. **Missing visualizations**: Install matplotlib and seaborn: `pip install matplotlib seaborn`
5. **Bayesian optimization errors**: Install scikit-optimize: `pip install scikit-optimize`

### Performance Tips
- Use `--demo` flag for quick testing
- Reduce sample sizes for faster processing
- Limit number of coins analyzed
- Use fewer prediction horizons

## 📈 Expected Results

### Performance Benchmarks
- **Side Prediction**: 60-75% accuracy (Random Forest)
- **Volume Prediction**: R² 0.1-0.3 (LASSO)
- **Feature Selection**: 10-30% of features selected by LASSO
- **Processing Time**: 2-10 minutes per coin (depending on data size)

### Key Insights
- OBI features capture orderbook microstructure
- Order flow features predict short-term momentum
- LASSO automatically selects most predictive price levels
- Performance varies significantly across coins and horizons

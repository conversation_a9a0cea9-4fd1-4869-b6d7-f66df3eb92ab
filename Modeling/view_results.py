#!/usr/bin/env python3
"""
View and summarize coin prediction analysis results.

This script helps you:
1. View the latest results files
2. Display summary statistics
3. Show available visualization files
4. Open visualizations

Usage:
    python view_results.py
"""

import os
import glob
import pandas as pd
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.image as mpimg

def find_latest_results():
    """Find the most recent results files"""
    
    print("🔍 SEARCHING FOR RESULTS FILES")
    print("=" * 50)
    
    # Find text result files
    text_files = glob.glob("modeling/coin_prediction_results_*.txt")
    text_files.sort(reverse=True)  # Most recent first
    
    # Find CSV files
    csv_files = glob.glob("modeling/*prediction_results*.csv")
    csv_files.sort(reverse=True)
    
    # Find visualization files
    viz_files = glob.glob("modeling/*.png")
    viz_files.sort()
    
    print(f"📄 Text Results: {len(text_files)} files")
    if text_files:
        print(f"  Latest: {text_files[0]}")
    
    print(f"📊 CSV Results: {len(csv_files)} files")
    if csv_files:
        for csv_file in csv_files:
            print(f"  - {csv_file}")
    
    print(f"🎨 Visualizations: {len(viz_files)} files")
    if viz_files:
        for viz_file in viz_files:
            print(f"  - {viz_file}")
    
    return {
        'text': text_files,
        'csv': csv_files,
        'visualizations': viz_files
    }

def display_latest_text_results():
    """Display the latest text results"""
    
    text_files = glob.glob("modeling/coin_prediction_results_*.txt")
    if not text_files:
        print("❌ No text result files found")
        return
    
    latest_file = max(text_files, key=os.path.getctime)
    print(f"\n📄 DISPLAYING LATEST TEXT RESULTS")
    print(f"File: {latest_file}")
    print("=" * 80)
    
    try:
        with open(latest_file, 'r') as f:
            content = f.read()
            print(content)
    except Exception as e:
        print(f"❌ Error reading file: {e}")

def display_csv_summary():
    """Display summary of CSV results"""
    
    csv_files = glob.glob("modeling/*prediction_results*.csv")
    if not csv_files:
        print("❌ No CSV result files found")
        return
    
    print(f"\n📊 CSV RESULTS SUMMARY")
    print("=" * 50)
    
    for csv_file in csv_files:
        print(f"\n📈 {csv_file}")
        print("-" * 40)
        
        try:
            df = pd.read_csv(csv_file)
            print(f"Shape: {df.shape}")
            print(f"Columns: {list(df.columns)}")
            
            if len(df) > 0:
                print("\nSample data:")
                print(df.head())
                
                # Show summary statistics for numeric columns
                numeric_cols = df.select_dtypes(include=['float64', 'int64']).columns
                if len(numeric_cols) > 0:
                    print(f"\nSummary statistics:")
                    print(df[numeric_cols].describe())
                    
        except Exception as e:
            print(f"❌ Error reading {csv_file}: {e}")

def list_visualizations():
    """List available visualization files"""
    
    viz_files = glob.glob("modeling/*.png")
    if not viz_files:
        print("❌ No visualization files found")
        return
    
    print(f"\n🎨 AVAILABLE VISUALIZATIONS")
    print("=" * 50)
    
    # Categorize visualizations
    categories = {
        'OBI Features': [f for f in viz_files if 'obi_features' in f],
        'Model Performance': [f for f in viz_files if 'model_performance' in f],
        'Cross-Coin Analysis': [f for f in viz_files if 'cross_coin' in f],
        'Feature Importance': [f for f in viz_files if 'feature_importance' in f],
        'Individual Coins': [f for f in viz_files if 'layered_prediction_' in f],
        'Other': [f for f in viz_files if not any(keyword in f for keyword in 
                 ['obi_features', 'model_performance', 'cross_coin', 'feature_importance', 'layered_prediction_'])]
    }
    
    for category, files in categories.items():
        if files:
            print(f"\n📊 {category}:")
            for file in files:
                file_size = os.path.getsize(file) / 1024  # KB
                mod_time = datetime.fromtimestamp(os.path.getmtime(file))
                print(f"  - {file} ({file_size:.1f} KB, {mod_time.strftime('%Y-%m-%d %H:%M')})")

def show_visualization_grid():
    """Display a grid of visualizations"""
    
    viz_files = glob.glob("modeling/*.png")
    if not viz_files:
        print("❌ No visualization files found")
        return
    
    # Limit to first 6 visualizations for display
    display_files = viz_files[:6]
    
    print(f"\n🖼️  DISPLAYING VISUALIZATION GRID")
    print(f"Showing {len(display_files)} of {len(viz_files)} visualizations")
    print("=" * 50)
    
    try:
        # Create subplot grid
        rows = 2
        cols = 3
        fig, axes = plt.subplots(rows, cols, figsize=(15, 10))
        fig.suptitle('Coin Prediction Analysis - Visualization Summary', fontsize=16)
        
        # Flatten axes for easier indexing
        if len(display_files) == 1:
            axes = [axes]
        else:
            axes = axes.flatten()
        
        for i, viz_file in enumerate(display_files):
            if i < len(axes):
                try:
                    img = mpimg.imread(viz_file)
                    axes[i].imshow(img)
                    axes[i].set_title(os.path.basename(viz_file), fontsize=8)
                    axes[i].axis('off')
                except Exception as e:
                    axes[i].text(0.5, 0.5, f'Error loading\n{os.path.basename(viz_file)}', 
                               ha='center', va='center', transform=axes[i].transAxes)
                    axes[i].axis('off')
        
        # Hide unused subplots
        for i in range(len(display_files), len(axes)):
            axes[i].axis('off')
        
        plt.tight_layout()
        plt.savefig('modeling/visualization_summary.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("✅ Visualization grid saved as 'modeling/visualization_summary.png'")
        
    except Exception as e:
        print(f"❌ Error creating visualization grid: {e}")

def main():
    """Main function to view results"""
    
    print("👁️  COIN PREDICTION ANALYSIS - RESULTS VIEWER")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Find all results
    results = find_latest_results()
    
    if not any(results.values()):
        print("❌ No results found. Please run the analysis first:")
        print("   python run_coin_analysis.py")
        return
    
    # Display options
    print(f"\n📋 AVAILABLE ACTIONS:")
    print("1. Display latest text results")
    print("2. Show CSV summary")
    print("3. List visualizations")
    print("4. Show visualization grid")
    print("5. All of the above")
    
    try:
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == '1':
            display_latest_text_results()
        elif choice == '2':
            display_csv_summary()
        elif choice == '3':
            list_visualizations()
        elif choice == '4':
            show_visualization_grid()
        elif choice == '5':
            display_latest_text_results()
            display_csv_summary()
            list_visualizations()
            show_visualization_grid()
        else:
            print("Invalid choice. Showing all results...")
            display_latest_text_results()
            display_csv_summary()
            list_visualizations()
            
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()

# OBI Layered Prediction Analysis - Improvements

This document describes the major improvements made to `layered_prediction_analysis.py` to implement OBI (Order Book Imbalance) features, order flow features, LASSO regression, and Bayesian optimization.

## 🎯 Key Improvements

### 1. OBI Features from Layered Orderbook Data

**Implementation**: `create_obi_layers_from_orderbook()`

- **40 Price Layers**: Creates 40 equal-sized layers within ±1% of midprice (20 above, 20 below)
- **Individual Layer OBI**: For each layer i: `(volume_above_i - volume_below_i) / (volume_above_i + volume_below_i)`
- **Cumulative Layer OBI**: For layers 1 to i: `(sum_volume_above - sum_volume_below) / (sum_volume_above + sum_volume_below)`
- **Total Features**: ~40 OBI features per timestamp

**Example**:
```python
# Layer 1 OBI: (a1-a2)/(a1+a2)
# Layer 2 OBI: [(a1+b1)-(a2+b2)]/[(a1+b1)+(a2+b2)]
# Where a1, b1 = volumes above midprice; a2, b2 = volumes below midprice
```

### 2. Order Flow (OF) Features

**Implementation**: `calculate_order_flow_features()`

- **Past 1-Minute Window**: Calculates buy-sell imbalance over rolling 1-minute windows
- **Core Feature**: `order_flow = past_1min_buy_size - past_1min_sell_size`
- **Additional Features**:
  - `net_volume`: buy_volume - sell_volume
  - `volume_ratio`: buy_volume / sell_volume
  - `buy_ratio`: buy_count / total_count
  - `trade_count`: total number of trades

### 3. LASSO Regression with L1 Penalty

**Implementation**: `bayesian_optimize_lasso()` + updated `train_layered_models()`

- **Automatic Feature Selection**: L1 penalty automatically selects relevant features
- **Replaces Ridge Regression**: Uses LASSO instead of Ridge for volume prediction
- **Hyperparameter Optimization**: Alpha parameter optimized via Bayesian optimization
- **Benefits**: Reduces overfitting, improves interpretability

### 4. Bayesian Optimization for Hyperparameter Tuning

**Implementation**: `bayesian_optimize_lasso()` and `bayesian_optimize_classifier()`

- **LASSO Alpha Optimization**: Finds optimal L1 penalty strength
- **Classifier Optimization**: Optimizes LogisticRegression C and max_iter parameters
- **Efficient Search**: Uses Gaussian Process to efficiently explore hyperparameter space
- **Dependency**: Requires `scikit-optimize` package

## 🚀 Usage

### Basic Usage

```python
from layered_prediction_analysis import analyze_obi_layered_prediction

# Run OBI analysis
result = analyze_obi_layered_prediction(
    pair="BTC_USD",
    sample_size=5000,
    num_layers=40,
    horizons=[10, 30, 60, 120, 200],
    use_bayesian_opt=True
)
```

### Testing

```bash
# Test the new features
python test_obi_features.py

# Run full analysis
python layered_prediction_analysis.py
```

## 📊 Data Requirements

### Orderbook Data Files
- Format: `{PAIR}_orderbook_*.feather`
- Columns: `Price`, `Size`, `Time`
- Used for: OBI feature calculation

### Trade Data Files
- Format: `{PAIR}_trades_*.feather`
- Columns: `Price`, `Size`, `Side`, `Time`
- Used for: Order flow features and target variables

## 🔧 Dependencies

### Required
- pandas
- numpy
- scikit-learn
- matplotlib
- tqdm

### Optional (for Bayesian Optimization)
- scikit-optimize

Install with:
```bash
pip install scikit-optimize
```

## 📈 Expected Performance Improvements

### 1. Better Microstructure Capture
- OBI features capture orderbook depth and imbalance
- 40 layers provide fine-grained price level information
- Cumulative features capture broader market sentiment

### 2. Enhanced Predictive Power
- Order flow features capture recent trading momentum
- LASSO automatically selects most predictive features
- Bayesian optimization finds optimal model parameters

### 3. Reduced Overfitting
- L1 penalty prevents overfitting to noise
- Feature selection improves generalization
- Optimized hyperparameters balance bias-variance tradeoff

## 🎛️ Configuration Options

### OBI Features
- `num_layers`: Number of price layers (default: 40)
- `layer_width_pct`: Layer width as percentage (default: 0.01 = 1%)

### Order Flow Features
- `time_window_minutes`: Window for order flow calculation (default: 1)

### Model Training
- `use_bayesian_opt`: Enable Bayesian optimization (default: True)
- `horizons`: Prediction horizons in seconds (default: [10, 30, 60, 120, 200])

## 📋 Output Files

### Results
- `obi_layered_prediction_results.csv`: Model performance metrics
- Feature DataFrames with OBI and order flow columns

### Visualizations
- Performance plots across different horizons
- Feature importance from LASSO models
- OBI and order flow time series

## 🔍 Key Functions

### New Functions
- `load_orderbook_data()`: Load orderbook files
- `calculate_midprice_from_orderbook()`: Calculate midprice from orderbook
- `create_obi_layers_from_orderbook()`: Generate OBI features
- `calculate_order_flow_features()`: Generate order flow features
- `bayesian_optimize_lasso()`: Optimize LASSO hyperparameters
- `bayesian_optimize_classifier()`: Optimize classifier hyperparameters
- `analyze_obi_layered_prediction()`: Main OBI analysis function

### Enhanced Functions
- `train_layered_models()`: Now uses LASSO and Bayesian optimization
- `main()`: Updated to showcase new OBI features

## 🎯 Trading Strategy Integration

The improved features align with your preferred trading strategy:

### OBI Thresholds
- Use OBI features to identify: sell (-1 < obi < -1/3), hold (-1/3 < obi < 1/3), buy (1/3 < obi < 1)
- 40 layers provide multiple OBI signals at different price levels
- Cumulative OBI captures broader market sentiment

### Order Flow Integration
- 1-minute order flow complements OBI features
- Captures recent trading momentum and direction
- Helps predict short-term price movements

### Model Selection
- LASSO automatically selects most predictive OBI and OF features
- Bayesian optimization ensures optimal model performance
- Multiple prediction horizons (10-200 seconds) support various trading timeframes

#!/usr/bin/env python3
"""
Run comprehensive coin prediction analysis with detailed results and visualizations.

This script will:
1. Run OBI-based layered prediction analysis for multiple coins
2. Print detailed results for each coin in terminal
3. Save results to text files and CSV
4. Generate comprehensive visualizations (PNG files)
5. Create individual coin performance charts

Usage:
    python run_coin_analysis.py
"""

import sys
import os
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from layered_prediction_analysis import (
    main,
    get_available_trading_pairs,
    analyze_obi_layered_prediction,
    analyze_layered_prediction,
    print_coin_prediction_results,
    create_comprehensive_visualizations,
    visualize_layered_results,
    install_dependencies,
    BAYESIAN_OPT_AVAILABLE
)

def run_comprehensive_analysis():
    """Run comprehensive analysis with detailed output"""
    
    print("🚀 STARTING COMPREHENSIVE COIN PREDICTION ANALYSIS")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Check dependencies
    if not BAYESIAN_OPT_AVAILABLE:
        print("⚠️  Installing required dependencies...")
        install_dependencies()
    
    # Get available pairs
    try:
        pairs = get_available_trading_pairs()
        print(f"📊 Found {len(pairs)} available trading pairs")
        
        if not pairs:
            print("❌ No trading pairs found. Please check data directory.")
            return
        
        # Select pairs to analyze
        pairs_to_analyze = pairs[:5]  # Analyze first 5 pairs
        print(f"🎯 Analyzing {len(pairs_to_analyze)} pairs: {pairs_to_analyze}")
        
        all_results = []
        
        # Run OBI analysis for each pair
        print(f"\n🔬 RUNNING OBI FEATURE EXTRACTION")
        print("-" * 50)
        
        for i, pair in enumerate(pairs_to_analyze, 1):
            print(f"\n[{i}/{len(pairs_to_analyze)}] Analyzing {pair}...")
            
            try:
                # OBI analysis
                obi_result = analyze_obi_layered_prediction(
                    pair=pair,
                    sample_size=3000,  # Moderate sample size
                    num_layers=40,
                    lookback_window=200,
                    horizons=[30, 60, 120],  # 3 horizons for faster processing
                    max_orderbook_files=2,
                    max_trade_files=2,
                    use_bayesian_opt=BAYESIAN_OPT_AVAILABLE,
                )
                
                if obi_result:
                    all_results.append(obi_result)
                    print(f"✅ {pair}: OBI analysis complete")
                else:
                    print(f"❌ {pair}: OBI analysis failed")
                    
            except Exception as e:
                print(f"❌ {pair}: Error - {str(e)}")
        
        # Run traditional analysis for comparison
        print(f"\n🤖 RUNNING TRADITIONAL MODEL TRAINING")
        print("-" * 50)
        
        if pairs_to_analyze:
            try:
                traditional_result = analyze_layered_prediction(
                    pair=pairs_to_analyze[0],
                    sample_size=2000,
                    num_layers=40,
                    lookback_window=200,
                    horizons=[30, 60, 120],
                )
                
                if traditional_result:
                    all_results.append(traditional_result)
                    print(f"✅ Traditional analysis complete for {pairs_to_analyze[0]}")
                else:
                    print(f"❌ Traditional analysis failed")
                    
            except Exception as e:
                print(f"❌ Traditional analysis error: {str(e)}")
        
        # Process results
        if all_results:
            print(f"\n📊 PROCESSING RESULTS")
            print("-" * 50)
            print(f"Total results: {len(all_results)}")
            
            # Print detailed results
            print(f"\n📋 Generating detailed terminal output...")
            print_coin_prediction_results(all_results, save_to_file=True)
            
            # Create visualizations
            print(f"\n🎨 Creating comprehensive visualizations...")
            create_comprehensive_visualizations(all_results, save_dir="modeling")
            
            # Individual visualizations for traditional results
            traditional_results = [r for r in all_results if "models" in r and "pair" in r]
            if traditional_results:
                print(f"\n📈 Creating individual coin charts...")
                for result in traditional_results:
                    pair_name = result.get('pair', 'Unknown')
                    print(f"  - {pair_name}")
                    visualize_layered_results(result)
            
            print(f"\n🎉 ANALYSIS COMPLETE!")
            print("=" * 80)
            print("📁 Generated Files:")
            print("  📊 Results:")
            print("    - coin_prediction_results_[timestamp].txt")
            print("    - obi_layered_prediction_results.csv")
            print("  🎨 Visualizations:")
            print("    - obi_features_analysis.png")
            print("    - model_performance_analysis.png")
            print("    - cross_coin_analysis.png") 
            print("    - feature_importance_analysis.png")
            if traditional_results:
                for result in traditional_results:
                    pair_name = result.get('pair', 'Unknown')
                    print(f"    - layered_prediction_{pair_name}.png")
            print("=" * 80)
            
            return all_results
        else:
            print("❌ No results generated")
            return None
            
    except Exception as e:
        print(f"❌ Analysis failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def quick_demo():
    """Run a quick demo with minimal data"""
    
    print("🚀 QUICK DEMO - COIN PREDICTION ANALYSIS")
    print("=" * 60)
    
    try:
        pairs = get_available_trading_pairs()
        if not pairs:
            print("❌ No trading pairs found")
            return
        
        # Just analyze one pair quickly
        demo_pair = pairs[0]
        print(f"📊 Demo with {demo_pair}")
        
        # Quick OBI analysis
        result = analyze_obi_layered_prediction(
            pair=demo_pair,
            sample_size=1000,  # Small sample
            num_layers=40,
            lookback_window=100,
            horizons=[60],  # Just one horizon
            max_orderbook_files=1,
            max_trade_files=1,
            use_bayesian_opt=False,  # Skip optimization for speed
        )
        
        if result:
            print("✅ Demo analysis complete!")
            
            # Show sample results
            if "obi_features" in result:
                obi_df = result["obi_features"]
                obi_cols = [col for col in obi_df.columns if col.startswith("obi_")]
                print(f"📈 Generated {len(obi_cols)} OBI features from {len(obi_df)} timestamps")
                
            if "order_flow" in result:
                of_df = result["order_flow"]
                print(f"📊 Generated order flow features from {len(of_df)} timestamps")
                
            return [result]
        else:
            print("❌ Demo failed")
            return None
            
    except Exception as e:
        print(f"❌ Demo error: {str(e)}")
        return None

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Run coin prediction analysis")
    parser.add_argument("--demo", action="store_true", help="Run quick demo")
    parser.add_argument("--full", action="store_true", help="Run full analysis")
    
    args = parser.parse_args()
    
    if args.demo:
        results = quick_demo()
    elif args.full:
        results = run_comprehensive_analysis()
    else:
        # Default: run the main function from the module
        print("🔄 Running default analysis...")
        results = main()
    
    if results:
        print(f"\n✅ Analysis completed successfully with {len(results)} results!")
    else:
        print(f"\n❌ Analysis completed with no results.")

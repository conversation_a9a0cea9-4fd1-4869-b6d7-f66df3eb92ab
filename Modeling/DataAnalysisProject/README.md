# Data Analysis Project

## Overview
The Richard Data Analysis Tool is designed to analyze and visualize orderbook and trade data from various trading pairs. It provides comprehensive insights into the data structure and generates visualizations to help users understand market dynamics.

## Features
- Discover available trading pairs in the specified data directory.
- Load and combine orderbook and trade data for analysis.
- Analyze the structure of the data, including shape, columns, data types, and statistical summaries.
- Create visualizations for orderbook and trade data, including price distributions, size distributions, and time series analysis.
- Generate a summary report of all trading pairs, including the number of files and sample records.

## Usage
1. Ensure you have the required libraries installed:
   - pandas
   - numpy
   - matplotlib
   - seaborn

   You can install these using pip:
   ```
   pip install pandas numpy matplotlib seaborn
   ```

2. Place your orderbook and trade data files in the `Data_Fetching/Richard_data` directory. The files should follow the naming convention:
   - Orderbook files: `*_orderbook_*.feather`
   - Trade files: `*_trades_*.feather`

3. Run the script:
   ```
   python analyze_richard_data.py
   ```

4. The script will discover all trading pairs, load the data, analyze the structure, and generate visualizations for each pair. The results will be saved as PNG files, and a summary report will be generated in CSV format.

## Modifications
The script has been modified to analyze all trading pairs instead of just the first three. This allows for a comprehensive analysis of all available data.

## License
This project is licensed under the MIT License.